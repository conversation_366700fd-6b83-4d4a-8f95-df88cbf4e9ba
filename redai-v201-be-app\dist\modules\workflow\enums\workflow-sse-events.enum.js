"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isWorkflowLifecycleEventType = exports.isWorkflowNodeEventType = exports.WorkflowSSEEventUtils = exports.EventCategory = exports.WorkflowLifecycleEventType = exports.WorkflowNodeEventType = void 0;
var WorkflowNodeEventType;
(function (WorkflowNodeEventType) {
    WorkflowNodeEventType["NODE_STARTED"] = "node.started";
    WorkflowNodeEventType["NODE_COMPLETED"] = "node.completed";
    WorkflowNodeEventType["NODE_FAILED"] = "node.failed";
    WorkflowNodeEventType["NODE_PROGRESS"] = "node.progress";
    WorkflowNodeEventType["NODE_PAUSED"] = "node.paused";
    WorkflowNodeEventType["NODE_RESUMED"] = "node.resumed";
    WorkflowNodeEventType["NODE_CANCELLED"] = "node.cancelled";
    WorkflowNodeEventType["NODE_RETRYING"] = "node.retrying";
})(WorkflowNodeEventType || (exports.WorkflowNodeEventType = WorkflowNodeEventType = {}));
var WorkflowLifecycleEventType;
(function (WorkflowLifecycleEventType) {
    WorkflowLifecycleEventType["WORKFLOW_STARTED"] = "workflow.started";
    WorkflowLifecycleEventType["WORKFLOW_COMPLETED"] = "workflow.completed";
    WorkflowLifecycleEventType["WORKFLOW_FAILED"] = "workflow.failed";
    WorkflowLifecycleEventType["WORKFLOW_PAUSED"] = "workflow.paused";
    WorkflowLifecycleEventType["WORKFLOW_RESUMED"] = "workflow.resumed";
    WorkflowLifecycleEventType["WORKFLOW_CANCELLED"] = "workflow.cancelled";
    WorkflowLifecycleEventType["WORKFLOW_STATE_CHANGED"] = "workflow.state.changed";
})(WorkflowLifecycleEventType || (exports.WorkflowLifecycleEventType = WorkflowLifecycleEventType = {}));
var EventCategory;
(function (EventCategory) {
    EventCategory["SSE_MESSAGE"] = "sse_message";
    EventCategory["WORKFLOW_NODE"] = "workflow_node";
    EventCategory["WORKFLOW_LIFECYCLE"] = "workflow_lifecycle";
    EventCategory["AGENT"] = "agent";
    EventCategory["WEBHOOK"] = "webhook";
    EventCategory["INTEGRATION"] = "integration";
    EventCategory["USER"] = "user";
    EventCategory["MEDIA_TRACKING"] = "media_tracking";
})(EventCategory || (exports.EventCategory = EventCategory = {}));
class WorkflowSSEEventUtils {
    static getEventCategory(eventType) {
        if (Object.values(WorkflowNodeEventType).includes(eventType)) {
            return EventCategory.WORKFLOW_NODE;
        }
        if (Object.values(WorkflowLifecycleEventType).includes(eventType)) {
            return EventCategory.WORKFLOW_LIFECYCLE;
        }
        return null;
    }
    static isWorkflowEvent(eventType) {
        const category = this.getEventCategory(eventType);
        return category === EventCategory.WORKFLOW_NODE ||
            category === EventCategory.WORKFLOW_LIFECYCLE;
    }
    static requiresUserAuth(eventType) {
        const category = this.getEventCategory(eventType);
        return category !== EventCategory.SSE_MESSAGE;
    }
    static getEventTypesForCategory(category) {
        switch (category) {
            case EventCategory.WORKFLOW_NODE:
                return Object.values(WorkflowNodeEventType);
            case EventCategory.WORKFLOW_LIFECYCLE:
                return Object.values(WorkflowLifecycleEventType);
            default:
                return [];
        }
    }
}
exports.WorkflowSSEEventUtils = WorkflowSSEEventUtils;
const isWorkflowNodeEventType = (eventType) => {
    return Object.values(WorkflowNodeEventType).includes(eventType);
};
exports.isWorkflowNodeEventType = isWorkflowNodeEventType;
const isWorkflowLifecycleEventType = (eventType) => {
    return Object.values(WorkflowLifecycleEventType).includes(eventType);
};
exports.isWorkflowLifecycleEventType = isWorkflowLifecycleEventType;
;
