"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserWorkflowSSEService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserWorkflowSSEService = void 0;
const common_1 = require("@nestjs/common");
let UserWorkflowSSEService = UserWorkflowSSEService_1 = class UserWorkflowSSEService {
    logger = new common_1.Logger(UserWorkflowSSEService_1.name);
    clients = new Map();
    pingInterval = 30000;
    pingTimer;
    constructor() {
        this.startPingTimer();
    }
    createSSEConnection(userId, response, nodeId, workflowId) {
        const clientId = this.generateClientId();
        response.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control',
        });
        const client = {
            id: clientId,
            userId,
            workflowId,
            nodeId,
            response,
            lastPing: new Date(),
        };
        this.clients.set(clientId, client);
        this.sendConnectionEstablished(client);
        response.on('close', () => {
            this.logger.log(`🔌 Client disconnected: ${clientId} (User: ${userId})`);
            this.removeClient(clientId);
        });
        response.on('error', (error) => {
            this.logger.error(`🔥 SSE Response error for client ${clientId}:`, error);
            this.removeClient(clientId);
        });
        this.logger.log(`✅ SSE connection created: ${clientId} (User: ${userId}, Workflow: ${workflowId})`);
        return clientId;
    }
    generateClientId() {
        return `sse_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    sendConnectionEstablished(client) {
        try {
            const event = {
                type: 'connection.established',
                data: {
                    clientId: client.id,
                    userId: client.userId,
                    workflowId: client.workflowId,
                    timestamp: new Date().toISOString(),
                },
                timestamp: new Date().toISOString(),
            };
            const sseData = `data: ${JSON.stringify(event)}\n\n`;
            client.response.write(sseData);
            client.lastPing = new Date();
        }
        catch (error) {
            this.logger.error(`Failed to send connection established to client ${client.id}:`, error);
        }
    }
    startPingTimer() {
        this.pingTimer = setInterval(() => {
            this.sendPingToAllClients();
        }, this.pingInterval);
    }
    sendPingToAllClients() {
        const now = new Date();
        const clientsToRemove = [];
        this.clients.forEach((client, clientId) => {
            try {
                const timeSinceLastPing = now.getTime() - client.lastPing.getTime();
                if (timeSinceLastPing > this.pingInterval * 2) {
                    this.logger.warn(`Client ${clientId} is stale, removing...`);
                    clientsToRemove.push(clientId);
                    return;
                }
                const pingEvent = {
                    type: 'ping',
                    timestamp: now.toISOString(),
                };
                const sseData = `data: ${JSON.stringify(pingEvent)}\n\n`;
                client.response.write(sseData);
            }
            catch (error) {
                this.logger.error(`Failed to ping client ${clientId}:`, error);
                clientsToRemove.push(clientId);
            }
        });
        clientsToRemove.forEach(clientId => {
            this.removeClient(clientId);
        });
        if (this.clients.size > 0) {
            this.logger.debug(`Pinged ${this.clients.size} SSE clients`);
        }
    }
    isUserOnline(userId) {
        const userClients = Array.from(this.clients.values()).filter(client => client.userId === userId);
        return userClients.length > 0;
    }
    getClient(clientId) {
        return this.clients.get(clientId);
    }
    getAllClients() {
        return Array.from(this.clients.values());
    }
    getClientsCount() {
        return this.clients.size;
    }
    getClientsByUser(userId) {
        return Array.from(this.clients.values()).filter(client => client.userId === userId);
    }
    getClientsByWorkflow(workflowId) {
        return Array.from(this.clients.values()).filter(client => client.workflowId === workflowId);
    }
    sendToClient(clientId, event) {
        const client = this.clients.get(clientId);
        if (!client) {
            this.logger.warn(`Client ${clientId} not found`);
            return false;
        }
        try {
            const sseData = `data: ${JSON.stringify(event)}\n\n`;
            client.response.write(sseData);
            client.lastPing = new Date();
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to send event to client ${clientId}:`, error);
            this.removeClient(clientId);
            return false;
        }
    }
    sendToUser(userId, event) {
        const userClients = this.getClientsByUser(userId);
        let sentCount = 0;
        userClients.forEach(client => {
            if (this.sendToClient(client.id, event)) {
                sentCount++;
            }
        });
        return sentCount;
    }
    sendToWorkflow(workflowId, event) {
        const workflowClients = this.getClientsByWorkflow(workflowId);
        let sentCount = 0;
        workflowClients.forEach(client => {
            if (this.sendToClient(client.id, event)) {
                sentCount++;
            }
        });
        return sentCount;
    }
    getUserConnections(userId) {
        return Array.from(this.clients.values())
            .filter(client => client.userId === userId)
            .map(client => client.id);
    }
    getWorkflowConnections(workflowId) {
        return Array.from(this.clients.values())
            .filter(client => client.workflowId === workflowId)
            .map(client => client.id);
    }
    removeClient(clientId) {
        const client = this.clients.get(clientId);
        if (client) {
            try {
                client.response.end();
            }
            catch (error) {
                this.logger.warn(`Error closing client ${clientId} response:`, error);
            }
            this.clients.delete(clientId);
            this.logger.debug(`Client ${clientId} removed`);
        }
    }
    onModuleDestroy() {
        if (this.pingTimer) {
            clearInterval(this.pingTimer);
        }
        this.clients.forEach((_, clientId) => {
            this.removeClient(clientId);
        });
    }
};
exports.UserWorkflowSSEService = UserWorkflowSSEService;
exports.UserWorkflowSSEService = UserWorkflowSSEService = UserWorkflowSSEService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], UserWorkflowSSEService);
