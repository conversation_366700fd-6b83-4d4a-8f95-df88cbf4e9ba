"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionNodeData = void 0;
const typeorm_1 = require("typeorm");
let ExecutionNodeData = class ExecutionNodeData {
    id;
    executionId;
    nodeName;
    inputData;
    outputData;
    executedAt;
};
exports.ExecutionNodeData = ExecutionNodeData;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ExecutionNodeData.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'execution_id', type: 'uuid' }),
    __metadata("design:type", String)
], ExecutionNodeData.prototype, "executionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'node_name', type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], ExecutionNodeData.prototype, "nodeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'input_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ExecutionNodeData.prototype, "inputData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'output_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ExecutionNodeData.prototype, "outputData", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'executed_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], ExecutionNodeData.prototype, "executedAt", void 0);
exports.ExecutionNodeData = ExecutionNodeData = __decorate([
    (0, typeorm_1.Entity)('execution_node_data'),
    (0, typeorm_1.Index)('idx_execution_node_data_execution_id', ['executionId'])
], ExecutionNodeData);
