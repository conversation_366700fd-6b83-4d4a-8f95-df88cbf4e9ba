"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WorkflowSSEUserController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowSSEUserController = void 0;
const subscription_guard_1 = require("../../../subscription/guards/subscription.guard");
const api_error_response_decorator_1 = require("../../../../common/decorators/api-error-response.decorator");
const current_user_decorator_1 = require("../../../auth/decorators/current-user.decorator");
const guards_1 = require("../../../auth/guards");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const workflow_sse_user_service_1 = require("../services/workflow-sse-user.service");
let WorkflowSSEUserController = WorkflowSSEUserController_1 = class WorkflowSSEUserController {
    userWorkflowSSEService;
    logger = new common_1.Logger(WorkflowSSEUserController_1.name);
    constructor(userWorkflowSSEService) {
        this.userWorkflowSSEService = userWorkflowSSEService;
    }
    async streamWorkflowEvents(user, workflowId, req, res) {
        const logger = new common_1.Logger('WorkflowSSEUserController');
        this.logger.log(`Creating SSE connection for user ${user.id} - workflow ${workflowId}`);
        try {
            const queryParams = req.query;
            const nodeId = queryParams.nodeId;
            this.userWorkflowSSEService.createSSEConnection(user.id, res, workflowId, nodeId);
        }
        catch (error) {
            if (!res.headersSent && !res.writableEnded) {
                try {
                    res.status(500).json({
                        error: 'Internal server error',
                        message: 'Failed to establish SSE stream',
                        workflowId,
                    });
                }
                catch (responseError) {
                    logger.error(`Failed to send error response for workflow ${workflowId}:`, responseError);
                }
            }
        }
    }
};
exports.WorkflowSSEUserController = WorkflowSSEUserController;
__decorate([
    (0, common_1.Get)('execution'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Stream workflow events via Server-Sent Events',
        description: 'Establishes an SSE connection to stream real-time workflow events and all node executions.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'workflowId',
        description: 'ID của workflow cần theo dõi',
        example: 'wf_123456789',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'SSE stream established successfully',
        headers: {
            'Content-Type': { description: 'text/event-stream' },
            'Cache-Control': { description: 'no-cache' },
            Connection: { description: 'keep-alive' },
        },
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('workflowId')),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object, Object]),
    __metadata("design:returntype", Promise)
], WorkflowSSEUserController.prototype, "streamWorkflowEvents", null);
exports.WorkflowSSEUserController = WorkflowSSEUserController = WorkflowSSEUserController_1 = __decorate([
    (0, swagger_1.ApiTags)('User Workflow SSE'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard, subscription_guard_1.SubscriptionGuard),
    (0, common_1.Controller)('user/workflows/:workflowId'),
    __metadata("design:paramtypes", [workflow_sse_user_service_1.UserWorkflowSSEService])
], WorkflowSSEUserController);
