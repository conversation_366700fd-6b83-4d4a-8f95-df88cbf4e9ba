"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateExecutionDto = exports.CreateExecutionDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const execution_status_enum_1 = require("../enums/execution-status.enum");
class CreateExecutionDto {
    workflowId;
    status;
    startedAt;
    finishedAt;
    errorMessage;
}
exports.CreateExecutionDto = CreateExecutionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của workflow cần thực thi',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateExecutionDto.prototype, "workflowId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái ban đầu của execution',
        enum: execution_status_enum_1.ExecutionStatusEnum,
        default: execution_status_enum_1.ExecutionStatusEnum.SUCCEEDED,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(execution_status_enum_1.ExecutionStatusEnum),
    __metadata("design:type", String)
], CreateExecutionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian bắt đầu (Unix timestamp milliseconds)',
        example: 1640995200000,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateExecutionDto.prototype, "startedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian kết thúc (Unix timestamp milliseconds)',
        example: 1640995260000,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateExecutionDto.prototype, "finishedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông báo lỗi nếu có',
        example: 'Connection timeout',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateExecutionDto.prototype, "errorMessage", void 0);
class UpdateExecutionDto {
    status;
    finishedAt;
    errorMessage;
}
exports.UpdateExecutionDto = UpdateExecutionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái mới của execution',
        enum: execution_status_enum_1.ExecutionStatusEnum,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(execution_status_enum_1.ExecutionStatusEnum),
    __metadata("design:type", String)
], UpdateExecutionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian kết thúc (Unix timestamp milliseconds)',
        example: 1640995260000,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateExecutionDto.prototype, "finishedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông báo lỗi nếu có',
        example: 'Connection timeout',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateExecutionDto.prototype, "errorMessage", void 0);
