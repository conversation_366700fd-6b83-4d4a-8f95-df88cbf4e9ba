"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisChannelUtils = exports.RedisEventType = exports.RedisChannelBuilder = exports.RedisChannelPattern = void 0;
var RedisChannelPattern;
(function (RedisChannelPattern) {
    RedisChannelPattern["NODE_STARTED"] = "node.started.*";
    RedisChannelPattern["NODE_PROCESSING"] = "node.processing.*";
    RedisChannelPattern["NODE_COMPLETED"] = "node.completed.*";
    RedisChannelPattern["NODE_FAILED"] = "node.failed.*";
    RedisChannelPattern["WORKFLOW_COMPLETED"] = "workflow.completed.*";
    RedisChannelPattern["WORKFLOW_FAILED"] = "workflow.failed.*";
})(RedisChannelPattern || (exports.RedisChannelPattern = RedisChannelPattern = {}));
class RedisChannelBuilder {
    static buildNodeStartedChannel(nodeId) {
        return `node.started.${nodeId}`;
    }
    static buildNodeProcessingChannel(nodeId) {
        return `node.processing.${nodeId}`;
    }
    static buildNodeCompletedChannel(nodeId) {
        return `node.completed.${nodeId}`;
    }
    static buildNodeFailedChannel(nodeId) {
        return `node.failed.${nodeId}`;
    }
    static buildWorkflowCompletedChannel(workflowId) {
        return `workflow.completed.${workflowId}`;
    }
    static buildWorkflowFailedChannel(workflowId) {
        return `workflow.failed.${workflowId}`;
    }
}
exports.RedisChannelBuilder = RedisChannelBuilder;
var RedisEventType;
(function (RedisEventType) {
    RedisEventType["NODE_STARTED"] = "node.started";
    RedisEventType["NODE_PROCESSING"] = "node.processing";
    RedisEventType["NODE_COMPLETED"] = "node.completed";
    RedisEventType["NODE_FAILED"] = "node.failed";
    RedisEventType["WORKFLOW_COMPLETED"] = "workflow.completed";
    RedisEventType["WORKFLOW_FAILED"] = "workflow.failed";
})(RedisEventType || (exports.RedisEventType = RedisEventType = {}));
class RedisChannelUtils {
    static parseChannel(channel) {
        const parts = channel.split('.');
        if (parts.length < 2) {
            return null;
        }
        const result = { type: parts[0] };
        if (parts[0] === 'node') {
            if (parts.length >= 3) {
                result.action = parts[1];
                result.nodeId = parts[2];
            }
        }
        else if (parts[0] === 'workflow') {
            if (parts.length >= 3) {
                result.action = parts[1];
                result.workflowId = parts[2];
            }
        }
        return result;
    }
    static matchesPattern(channel, pattern) {
        const patternRegex = pattern.replace(/\*/g, '[^.]+');
        const regex = new RegExp(`^${patternRegex}$`);
        return regex.test(channel);
    }
    static getEventTypeFromChannel(channel) {
        const parsed = this.parseChannel(channel);
        if (!parsed)
            return null;
        if (parsed.type === 'node') {
            switch (parsed.action) {
                case 'started': return RedisEventType.NODE_STARTED;
                case 'processing': return RedisEventType.NODE_PROCESSING;
                case 'completed': return RedisEventType.NODE_COMPLETED;
                case 'failed': return RedisEventType.NODE_FAILED;
            }
        }
        else if (parsed.type === 'workflow') {
            switch (parsed.action) {
                case 'completed': return RedisEventType.WORKFLOW_COMPLETED;
                case 'failed': return RedisEventType.WORKFLOW_FAILED;
            }
        }
        return null;
    }
}
exports.RedisChannelUtils = RedisChannelUtils;
