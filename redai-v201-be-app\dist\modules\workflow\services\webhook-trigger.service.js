"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WebhookTriggerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookTriggerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
const enums_1 = require("../enums");
const interfaces_1 = require("../interfaces");
const workflow_queue_service_1 = require("./workflow-queue.service");
const workflow_redis_service_1 = require("./workflow-redis.service");
const workflow_sse_user_service_1 = require("../user/services/workflow-sse-user.service");
let WebhookTriggerService = WebhookTriggerService_1 = class WebhookTriggerService {
    webhookRegistryRepository;
    executionRepository;
    executionNodeDataRepository;
    workflowRedisService;
    workflowQueueService;
    userWorkflowSSEService;
    logger = new common_1.Logger(WebhookTriggerService_1.name);
    constructor(webhookRegistryRepository, executionRepository, executionNodeDataRepository, workflowRedisService, workflowQueueService, userWorkflowSSEService) {
        this.webhookRegistryRepository = webhookRegistryRepository;
        this.executionRepository = executionRepository;
        this.executionNodeDataRepository = executionNodeDataRepository;
        this.workflowRedisService = workflowRedisService;
        this.workflowQueueService = workflowQueueService;
        this.userWorkflowSSEService = userWorkflowSSEService;
    }
    async triggerWebhook(webhookId, request, body, headers) {
        try {
            this.logger.log(`Triggering webhook: ${webhookId}`);
            const webhook = await this.webhookRegistryRepository.findOne({
                where: { id: webhookId },
                relations: ['node', 'workflow'],
            });
            if (!webhook) {
                throw new common_1.HttpException({
                    error: 'Webhook not found',
                    webhookId,
                    timestamp: new Date().toISOString(),
                }, common_1.HttpStatus.NOT_FOUND);
            }
            const webhookData = {
                method: request.method,
                headers,
                body,
                query: request.query,
            };
            const userId = webhook.workflow?.userId || 0;
            const isUserOnline = this.workflowSSEService.isUserOnline(userId);
            const executionMode = isUserOnline ? 'realtime' : 'background';
            const execution = await this.executionRepository.create({
                workflowId: webhook.workflowId,
                status: enums_1.ExecutionStatusEnum.RUNNING,
                startedAt: Date.now(),
            });
            const savedExecution = await this.executionRepository.save(execution);
            const executionId = savedExecution.id;
            const executionNodeData = await this.executionNodeDataRepository.create({
                executionId,
                nodeName: webhook.node.name,
                inputData: null,
                outputData: webhookData,
                executedAt: Date.now(),
            });
            await this.executionNodeDataRepository.save(executionNodeData);
            if (executionMode === 'realtime') {
                const nodeStartedEvent = {
                    type: enums_1.WorkflowNodeEventType.NODE_STARTED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    executionId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        nodeType: 'webhook',
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                        inputData: webhookData,
                        startedAt: new Date().toISOString(),
                        triggeredBy: 'webhook',
                        executionMode: 'realtime'
                    },
                };
                this.userWorkflowSSEService.sendToUser(userId, nodeStartedEvent);
                this.logger.debug(`NODE_STARTED event sent to user ${userId} for webhook ${webhookId}`);
                const nodeProgressEvent = {
                    type: enums_1.WorkflowNodeEventType.NODE_PROGRESS,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    executionId,
                    userId,
                    timestamp: new Date().toISOString(),
                    progress: 50,
                    data: {
                        nodeType: 'webhook',
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                        stage: 'processing',
                        estimatedTimeRemaining: 1000,
                        executionMode: 'realtime'
                    },
                };
                this.userWorkflowSSEService.sendToUser(userId, nodeProgressEvent);
                this.logger.debug(`NODE_PROGRESS event sent to user ${userId} for webhook ${webhookId}`);
                await this.workflowRedisService.sendCommand({
                    userId,
                    workflowId: webhook.workflowId,
                    executionId,
                    initContext: webhookData,
                });
                const nodeCompletedEvent = {
                    type: enums_1.WorkflowNodeEventType.NODE_COMPLETED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    executionId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        nodeType: 'webhook',
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                        outputData: webhookData,
                        executionTime: 1000,
                        completedAt: new Date().toISOString(),
                        success: true,
                        executionMode: 'realtime'
                    },
                    nodeResult: {
                        success: true,
                        output: webhookData,
                        executionTime: 1000,
                    },
                };
                this.userWorkflowSSEService.sendToUser(userId, nodeCompletedEvent);
                this.logger.debug(`NODE_COMPLETED event sent to user ${userId} for webhook ${webhookId}`);
            }
            else {
                const payload = {
                    userId,
                    workflowId: webhook.workflowId,
                    executionId,
                    type: interfaces_1.EExecutionType.EXECUTE,
                    typeExecution: interfaces_1.EExecutionMode.BACKGROUND,
                    initContext: webhookData,
                };
                await this.workflowQueueService.executeUser(payload);
            }
        }
        catch (error) {
            this.logger.error(`Failed to trigger webhook: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.WebhookTriggerService = WebhookTriggerService;
exports.WebhookTriggerService = WebhookTriggerService = WebhookTriggerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.WebhookRegistry)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.Execution)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.ExecutionNodeData)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        workflow_redis_service_1.WorkflowRedisService,
        workflow_queue_service_1.WorkflowQueueService,
        workflow_sse_user_service_1.UserWorkflowSSEService])
], WebhookTriggerService);
