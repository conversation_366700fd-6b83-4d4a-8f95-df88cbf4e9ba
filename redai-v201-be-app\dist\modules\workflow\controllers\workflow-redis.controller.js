"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WorkflowRedisController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowRedisController = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const enums_1 = require("../enums");
const workflow_redis_service_1 = require("./../services/workflow-redis.service");
let WorkflowRedisController = WorkflowRedisController_1 = class WorkflowRedisController {
    workflowRedisService;
    logger = new common_1.Logger(WorkflowRedisController_1.name);
    constructor(workflowRedisService) {
        this.workflowRedisService = workflowRedisService;
    }
    async handleNodeStarted(data) {
        this.workflowRedisService.handleNodeStartedEvent(data);
    }
    async handleNodeProcessing(data) {
        this.workflowRedisService.handleNodeProcessingEvent(data);
    }
    async handleNodeCompleted(data) {
        this.workflowRedisService.handleNodeCompletedEvent(data);
    }
    async handleNodeFailed(data) {
        this.workflowRedisService.handleNodeFailedEvent(data);
    }
    async handleWorkflowCompleted(data) {
        this.workflowRedisService.handleWorkflowCompletedEvent(data);
    }
    async handleWorkflowFailed(data) {
        this.workflowRedisService.handleWorkflowFailedEvent(data);
    }
    async healthCheck() {
        this.logger.debug('Redis health check requested');
        try {
            const healthStatus = await this.workflowRedisService.getRedisHealth();
            return {
                status: healthStatus.status === 'healthy' ? 'ok' : 'error',
                timestamp: new Date().toISOString(),
                details: {
                    redisConnection: healthStatus.status === 'healthy',
                    responseTime: Date.now() - new Date(healthStatus.timestamp).getTime(),
                    activeConnections: 1,
                    memoryUsage: process.memoryUsage().heapUsed,
                }
            };
        }
        catch (error) {
            this.logger.error('Health check failed:', error);
            return {
                status: 'error',
                timestamp: new Date().toISOString(),
                details: {
                    redisConnection: false,
                    errors: [error.message || 'Unknown error'],
                }
            };
        }
    }
};
exports.WorkflowRedisController = WorkflowRedisController;
__decorate([
    (0, microservices_1.MessagePattern)(enums_1.RedisChannelPattern.NODE_STARTED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowRedisController.prototype, "handleNodeStarted", null);
__decorate([
    (0, microservices_1.MessagePattern)(enums_1.RedisChannelPattern.NODE_PROCESSING),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowRedisController.prototype, "handleNodeProcessing", null);
__decorate([
    (0, microservices_1.MessagePattern)(enums_1.RedisChannelPattern.NODE_COMPLETED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowRedisController.prototype, "handleNodeCompleted", null);
__decorate([
    (0, microservices_1.MessagePattern)(enums_1.RedisChannelPattern.NODE_FAILED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowRedisController.prototype, "handleNodeFailed", null);
__decorate([
    (0, microservices_1.MessagePattern)(enums_1.RedisChannelPattern.WORKFLOW_COMPLETED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowRedisController.prototype, "handleWorkflowCompleted", null);
__decorate([
    (0, microservices_1.MessagePattern)(enums_1.RedisChannelPattern.WORKFLOW_FAILED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowRedisController.prototype, "handleWorkflowFailed", null);
__decorate([
    (0, microservices_1.MessagePattern)('redis.health.check'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WorkflowRedisController.prototype, "healthCheck", null);
exports.WorkflowRedisController = WorkflowRedisController = WorkflowRedisController_1 = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [workflow_redis_service_1.WorkflowRedisService])
], WorkflowRedisController);
