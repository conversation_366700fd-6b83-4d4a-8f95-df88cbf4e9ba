"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSEStatsDto = exports.SSEMessageDto = exports.WorkflowNodeEventDto = exports.SSEConnectionEstablishedDto = exports.WorkflowSSEEventType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const workflow_sse_events_enum_1 = require("../enums/workflow-sse-events.enum");
var WorkflowSSEEventType;
(function (WorkflowSSEEventType) {
    WorkflowSSEEventType["CONNECTION_ESTABLISHED"] = "connection.established";
    WorkflowSSEEventType["PING"] = "ping";
    WorkflowSSEEventType["WORKFLOW_EVENT"] = "workflow.event";
    WorkflowSSEEventType["NODE_STARTED"] = "node.started";
    WorkflowSSEEventType["NODE_COMPLETED"] = "node.completed";
    WorkflowSSEEventType["NODE_FAILED"] = "node.failed";
    WorkflowSSEEventType["NODE_PROGRESS"] = "node.progress";
    WorkflowSSEEventType["WORKFLOW_COMPLETED"] = "workflow.completed";
    WorkflowSSEEventType["WORKFLOW_FAILED"] = "workflow.failed";
})(WorkflowSSEEventType || (exports.WorkflowSSEEventType = WorkflowSSEEventType = {}));
class SSEConnectionEstablishedDto {
    clientId;
    userId;
    workflowId;
    nodeId;
    timestamp;
}
exports.SSEConnectionEstablishedDto = SSEConnectionEstablishedDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của SSE client',
        example: '123_1234567890_abc123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SSEConnectionEstablishedDto.prototype, "clientId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của user',
        example: 123,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SSEConnectionEstablishedDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của workflow (nếu có)',
        example: 'wf_123456789',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SSEConnectionEstablishedDto.prototype, "workflowId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của node (nếu có)',
        example: 'node_123456789',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SSEConnectionEstablishedDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp khi connection được tạo',
        example: '2025-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SSEConnectionEstablishedDto.prototype, "timestamp", void 0);
class WorkflowNodeEventDto {
    type;
    workflowId;
    nodeId;
    executionId;
    userId;
    data;
    timestamp;
    progress;
    error;
}
exports.WorkflowNodeEventDto = WorkflowNodeEventDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại event',
        enum: [...Object.values(workflow_sse_events_enum_1.WorkflowNodeEventType), ...Object.values(workflow_sse_events_enum_1.WorkflowLifecycleEventType)],
        example: workflow_sse_events_enum_1.WorkflowNodeEventType.NODE_COMPLETED,
    }),
    (0, class_validator_1.IsEnum)([...Object.values(workflow_sse_events_enum_1.WorkflowNodeEventType), ...Object.values(workflow_sse_events_enum_1.WorkflowLifecycleEventType)]),
    __metadata("design:type", String)
], WorkflowNodeEventDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của workflow',
        example: 'wf_123456789',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowNodeEventDto.prototype, "workflowId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của node (nếu có)',
        example: 'node_123456789',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowNodeEventDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của execution',
        example: 'exec_1234567890_abc123',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowNodeEventDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của user',
        example: 123,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkflowNodeEventDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dữ liệu kèm theo event',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], WorkflowNodeEventDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp của event',
        example: '2025-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowNodeEventDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiến độ thực hiện (0-100)',
        example: 75,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkflowNodeEventDto.prototype, "progress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông báo lỗi (nếu có)',
        example: 'Node execution failed: Invalid input',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowNodeEventDto.prototype, "error", void 0);
class SSEMessageDto {
    type;
    data;
    event;
    timestamp;
}
exports.SSEMessageDto = SSEMessageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại message',
        enum: workflow_sse_events_enum_1.SSEMessageType,
        example: workflow_sse_events_enum_1.SSEMessageType.WORKFLOW_EVENT,
    }),
    (0, class_validator_1.IsEnum)(workflow_sse_events_enum_1.SSEMessageType),
    __metadata("design:type", typeof (_a = typeof workflow_sse_events_enum_1.SSEMessageType !== "undefined" && workflow_sse_events_enum_1.SSEMessageType) === "function" ? _a : Object)
], SSEMessageDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dữ liệu của message',
        oneOf: [
            { $ref: '#/components/schemas/SSEConnectionEstablishedDto' },
            { $ref: '#/components/schemas/WorkflowNodeEventDto' },
        ],
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], SSEMessageDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event data (cho workflow events)',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", WorkflowNodeEventDto)
], SSEMessageDto.prototype, "event", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp của message',
        example: '2025-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SSEMessageDto.prototype, "timestamp", void 0);
class SSEStatsDto {
    totalClients;
    clientsByUser;
}
exports.SSEStatsDto = SSEStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số clients đang kết nối',
        example: 5,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SSEStatsDto.prototype, "totalClients", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số clients theo từng user',
        example: { '123': 2, '456': 3 },
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], SSEStatsDto.prototype, "clientsByUser", void 0);
