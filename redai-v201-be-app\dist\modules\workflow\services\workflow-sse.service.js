"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WorkflowSSEService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowSSEService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
let WorkflowSSEService = WorkflowSSEService_1 = class WorkflowSSEService {
    eventEmitter;
    logger = new common_1.Logger(WorkflowSSEService_1.name);
    clients = new Map();
    pingInterval = 30000;
    pingTimer;
    constructor(eventEmitter) {
        this.eventEmitter = eventEmitter;
        this.startPingTimer();
    }
    createSSEConnection(userId, response, workflowId, nodeId) {
        const clientId = `${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        response.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control',
        });
        this.sendSSEMessage(response, {
            type: 'connection.established',
            data: {
                clientId,
                userId,
                workflowId,
                nodeId,
                timestamp: new Date().toISOString(),
            },
        });
        const client = {
            id: clientId,
            userId,
            workflowId,
            nodeId,
            response,
            createdAt: new Date(),
            lastPing: new Date(),
        };
        this.clients.set(clientId, client);
        response.on('close', () => {
            this.removeClient(clientId);
        });
        response.on('error', (error) => {
            this.logger.error(`SSE connection error for client ${clientId}:`, error);
            this.removeClient(clientId);
        });
        this.logger.log(`SSE connection established for user ${userId}, client: ${clientId}`);
        return clientId;
    }
    sendSSEMessage(response, data) {
        try {
            const message = `data: ${JSON.stringify(data)}\n\n`;
            response.write(message);
        }
        catch (error) {
            this.logger.error('Error sending SSE message:', error);
        }
    }
    broadcastWorkflowEvent(event) {
        const relevantClients = Array.from(this.clients.values()).filter(client => {
            if (client.userId !== event.userId)
                return false;
            if (client.workflowId && client.workflowId !== event.workflowId)
                return false;
            if (client.nodeId && event.nodeId && client.nodeId !== event.nodeId)
                return false;
            return true;
        });
        relevantClients.forEach(client => {
            try {
                this.sendSSEMessage(client.response, {
                    type: 'workflow.event',
                    event,
                    timestamp: new Date().toISOString(),
                });
                client.lastPing = new Date();
            }
            catch (error) {
                this.logger.error(`Error sending event to client ${client.id}:`, error);
                this.removeClient(client.id);
            }
        });
        this.logger.debug(`Broadcasted ${event.type} to ${relevantClients.length} clients`);
    }
    isUserOnline(userId) {
        const userClients = Array.from(this.clients.values()).filter(client => client.userId === userId);
        return userClients.length > 0;
    }
    sendToClient(clientId, event) {
        const client = this.clients.get(clientId);
        if (!client) {
            this.logger.warn(`Client ${clientId} not found`);
            return;
        }
        try {
            this.sendSSEMessage(client.response, {
                type: 'workflow.event',
                event,
                timestamp: new Date().toISOString(),
            });
            client.lastPing = new Date();
        }
        catch (error) {
            this.logger.error(`Error sending event to client ${clientId}:`, error);
            this.removeClient(clientId);
        }
    }
    removeClient(clientId) {
        const client = this.clients.get(clientId);
        if (client) {
            try {
                client.response.end();
            }
            catch (error) {
            }
            this.clients.delete(clientId);
            this.logger.log(`Removed SSE client: ${clientId}`);
        }
    }
    startPingTimer() {
        this.pingTimer = setInterval(() => {
            const now = new Date();
            const staleClients = [];
            this.clients.forEach((client, clientId) => {
                const timeSinceLastPing = now.getTime() - client.lastPing.getTime();
                if (timeSinceLastPing > this.pingInterval * 2) {
                    staleClients.push(clientId);
                }
                else {
                    try {
                        this.sendSSEMessage(client.response, {
                            type: 'ping',
                            timestamp: now.toISOString(),
                        });
                    }
                    catch (error) {
                        staleClients.push(clientId);
                    }
                }
            });
            staleClients.forEach(clientId => this.removeClient(clientId));
            if (this.clients.size > 0) {
                this.logger.debug(`SSE ping sent to ${this.clients.size} clients, removed ${staleClients.length} stale clients`);
            }
        }, this.pingInterval);
    }
    handleNodeStarted(payload) {
        const event = {
            type: 'node.started',
            workflowId: payload.workflowId,
            nodeId: payload.nodeId,
            executionId: payload.executionId,
            userId: payload.userId,
            data: payload.data,
            timestamp: new Date().toISOString(),
        };
        this.broadcastWorkflowEvent(event);
    }
    handleNodeCompleted(payload) {
        const event = {
            type: 'node.completed',
            workflowId: payload.workflowId,
            nodeId: payload.nodeId,
            executionId: payload.executionId,
            userId: payload.userId,
            data: payload.result,
            timestamp: new Date().toISOString(),
        };
        this.broadcastWorkflowEvent(event);
    }
    handleNodeFailed(payload) {
        const event = {
            type: 'node.failed',
            workflowId: payload.workflowId,
            nodeId: payload.nodeId,
            executionId: payload.executionId,
            userId: payload.userId,
            error: payload.error,
            timestamp: new Date().toISOString(),
        };
        this.broadcastWorkflowEvent(event);
    }
    handleNodeProgress(payload) {
        const event = {
            type: 'node.progress',
            workflowId: payload.workflowId,
            nodeId: payload.nodeId,
            executionId: payload.executionId,
            userId: payload.userId,
            progress: payload.progress,
            data: payload.data,
            timestamp: new Date().toISOString(),
        };
        this.broadcastWorkflowEvent(event);
    }
    handleWorkflowCompleted(payload) {
        const event = {
            type: 'workflow.completed',
            workflowId: payload.workflowId,
            executionId: payload.executionId,
            userId: payload.userId,
            data: payload.result,
            timestamp: new Date().toISOString(),
        };
        this.broadcastWorkflowEvent(event);
    }
    handleWorkflowFailed(payload) {
        const event = {
            type: 'workflow.failed',
            workflowId: payload.workflowId,
            executionId: payload.executionId,
            userId: payload.userId,
            error: payload.error,
            timestamp: new Date().toISOString(),
        };
        this.broadcastWorkflowEvent(event);
    }
    onModuleDestroy() {
        if (this.pingTimer) {
            clearInterval(this.pingTimer);
        }
        this.clients.forEach((client, clientId) => {
            this.removeClient(clientId);
        });
    }
    getStats() {
        return {
            totalClients: this.clients.size,
            clientsByUser: Array.from(this.clients.values()).reduce((acc, client) => {
                acc[client.userId] = (acc[client.userId] || 0) + 1;
                return acc;
            }, {}),
        };
    }
};
exports.WorkflowSSEService = WorkflowSSEService;
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.node.started'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WorkflowSSEService.prototype, "handleNodeStarted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.node.completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WorkflowSSEService.prototype, "handleNodeCompleted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.node.failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WorkflowSSEService.prototype, "handleNodeFailed", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.node.progress'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WorkflowSSEService.prototype, "handleNodeProgress", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WorkflowSSEService.prototype, "handleWorkflowCompleted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WorkflowSSEService.prototype, "handleWorkflowFailed", null);
exports.WorkflowSSEService = WorkflowSSEService = WorkflowSSEService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [event_emitter_1.EventEmitter2])
], WorkflowSSEService);
