"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WorkflowRedisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowRedisService = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const rxjs_1 = require("rxjs");
const enums_1 = require("../enums");
const workflow_sse_user_service_1 = require("../user/services/workflow-sse-user.service");
let WorkflowRedisService = WorkflowRedisService_1 = class WorkflowRedisService {
    redisClient;
    userWorkflowSSEService;
    logger = new common_1.Logger(WorkflowRedisService_1.name);
    constructor(redisClient, userWorkflowSSEService) {
        this.redisClient = redisClient;
        this.userWorkflowSSEService = userWorkflowSSEService;
    }
    async sendCommand(command) {
        try {
            this.logger.debug('Sending command to worker:', command);
            const result = await (0, rxjs_1.firstValueFrom)(this.redisClient.send({ cmd: enums_1.WorkflowJobType.USER_EXECUTE }, command));
            this.logger.debug('Command sent successfully, result:', result);
            return result;
        }
        catch (error) {
            this.logger.error('Failed to send command to worker:', error);
            throw error;
        }
    }
    handleNodeStartedEvent(data) {
        try {
            this.logger.debug('Processing NODE_STARTED event:', data);
            const eventData = this.parseEventData(data);
            const channelInfo = this.parseChannelInfo(data);
            if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
                const sseEvent = {
                    type: enums_1.WorkflowNodeEventType.NODE_STARTED,
                    workflowId: eventData.workflowId,
                    nodeId: channelInfo.nodeId,
                    userId: eventData.userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        nodeName: eventData.nodeName,
                        nodeType: eventData.nodeType,
                    },
                };
                this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
            }
        }
        catch (error) {
            this.logger.error('Failed to handle NODE_STARTED event:', error);
        }
    }
    handleNodeProcessingEvent(data) {
        try {
            this.logger.debug('Processing NODE_PROCESSING event:', data);
            const eventData = this.parseEventData(data);
            const channelInfo = this.parseChannelInfo(data);
            this.logger.log(`Node processing: ${channelInfo?.nodeId} in workflow ${eventData.workflowId}`);
            if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
                const sseEvent = {
                    type: enums_1.WorkflowNodeEventType.NODE_PROGRESS,
                    workflowId: eventData.workflowId,
                    nodeId: channelInfo.nodeId,
                    userId: eventData.userId,
                    timestamp: new Date().toISOString(),
                    progress: {},
                };
                this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
                this.logger.debug(`NODE_PROCESSING event sent to SSE clients for user ${eventData.userId}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to handle NODE_PROCESSING event:', error);
        }
    }
    handleNodeCompletedEvent(data) {
        try {
            this.logger.debug('Processing NODE_COMPLETED event:', data);
            const eventData = this.parseEventData(data);
            const channelInfo = this.parseChannelInfo(data);
            this.logger.log(`Node completed: ${channelInfo?.nodeId} in workflow ${eventData.workflowId}`);
            if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
                const sseEvent = {
                    type: enums_1.WorkflowNodeEventType.NODE_COMPLETED,
                    workflowId: eventData.workflowId,
                    nodeId: channelInfo.nodeId,
                    userId: eventData.userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        output: eventData.outputData,
                        executionTime: eventData.executionTime || 0,
                    },
                };
                this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
                this.logger.debug(`NODE_COMPLETED event sent to SSE clients for user ${eventData.userId}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to handle NODE_COMPLETED event:', error);
        }
    }
    handleNodeFailedEvent(data) {
        try {
            this.logger.debug('Processing NODE_FAILED event:', data);
            const eventData = this.parseEventData(data);
            const channelInfo = this.parseChannelInfo(data);
            this.logger.error(`Node failed: ${channelInfo?.nodeId} in workflow ${eventData.workflowId}`, eventData.error);
            if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
                const sseEvent = {
                    type: enums_1.WorkflowNodeEventType.NODE_FAILED,
                    workflowId: eventData.workflowId,
                    nodeId: channelInfo.nodeId,
                    userId: eventData.userId,
                    timestamp: new Date().toISOString(),
                    error: {},
                };
                this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
                this.logger.debug(`NODE_FAILED event sent to SSE clients for user ${eventData.userId}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to handle NODE_FAILED event:', error);
        }
    }
    handleWorkflowCompletedEvent(data) {
        try {
            this.logger.debug('Processing WORKFLOW_COMPLETED event:', data);
            const eventData = this.parseEventData(data);
            const channelInfo = this.parseChannelInfo(data);
            this.logger.log(`Workflow completed: ${channelInfo?.workflowId} for user ${eventData.userId}`);
            if (eventData.workflowId && eventData.userId) {
                const sseEvent = {
                    type: enums_1.WorkflowLifecycleEventType.WORKFLOW_COMPLETED,
                    workflowId: eventData.workflowId,
                    userId: eventData.userId,
                    timestamp: new Date().toISOString(),
                    data: {},
                };
                this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
                this.logger.debug(`WORKFLOW_COMPLETED event sent to SSE clients for user ${eventData.userId}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to handle WORKFLOW_COMPLETED event:', error);
        }
    }
    handleWorkflowFailedEvent(data) {
        try {
            this.logger.debug('Processing WORKFLOW_FAILED event:', data);
            const eventData = this.parseEventData(data);
            const channelInfo = this.parseChannelInfo(data);
            this.logger.error(`Workflow failed: ${channelInfo?.workflowId} for user ${eventData.userId}`, eventData.error);
            if (eventData.workflowId && eventData.userId) {
                const sseEvent = {
                    type: enums_1.WorkflowLifecycleEventType.WORKFLOW_FAILED,
                    workflowId: eventData.workflowId,
                    userId: eventData.userId,
                    timestamp: new Date().toISOString(),
                    error: {},
                };
                this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
                this.logger.debug(`WORKFLOW_FAILED event sent to SSE clients for user ${eventData.userId}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to handle WORKFLOW_FAILED event:', error);
        }
    }
    parseEventData(data) {
        try {
            if (typeof data === 'string') {
                return JSON.parse(data);
            }
            if (data.message) {
                return typeof data.message === 'string' ? JSON.parse(data.message) : data.message;
            }
            return data;
        }
        catch (error) {
            this.logger.warn('Failed to parse event data, using raw data:', error);
            return data;
        }
    }
    parseChannelInfo(data) {
        try {
            if (data.channel) {
                return enums_1.RedisChannelUtils.parseChannel(data.channel);
            }
            return null;
        }
        catch (error) {
            this.logger.warn('Failed to parse channel info:', error);
            return null;
        }
    }
    handleRedisNodeEvent(data, eventType) {
        this.logger.warn('Using deprecated handleRedisNodeEvent method');
        switch (eventType) {
            case enums_1.RedisChannelPattern.NODE_STARTED:
                this.handleNodeStartedEvent(data);
                break;
            case enums_1.RedisChannelPattern.NODE_PROCESSING:
                this.handleNodeProcessingEvent(data);
                break;
            case enums_1.RedisChannelPattern.NODE_COMPLETED:
                this.handleNodeCompletedEvent(data);
                break;
            case enums_1.RedisChannelPattern.NODE_FAILED:
                this.handleNodeFailedEvent(data);
                break;
            case enums_1.RedisChannelPattern.WORKFLOW_COMPLETED:
                this.handleWorkflowCompletedEvent(data);
                break;
            case enums_1.RedisChannelPattern.WORKFLOW_FAILED:
                this.handleWorkflowFailedEvent(data);
                break;
            default:
                this.logger.warn(`Unknown event type: ${eventType}`);
        }
    }
    async publishNodeStarted(nodeId, eventData) {
        try {
            const channel = enums_1.RedisChannelBuilder.buildNodeStartedChannel(nodeId);
            await this.publishToRedis(channel, eventData);
            this.logger.debug(`Published NODE_STARTED event to ${channel}`);
        }
        catch (error) {
            this.logger.error('Failed to publish NODE_STARTED event:', error);
        }
    }
    async publishNodeProcessing(nodeId, eventData) {
        try {
            const channel = enums_1.RedisChannelBuilder.buildNodeProcessingChannel(nodeId);
            await this.publishToRedis(channel, eventData);
            this.logger.debug(`Published NODE_PROCESSING event to ${channel}`);
        }
        catch (error) {
            this.logger.error('Failed to publish NODE_PROCESSING event:', error);
        }
    }
    async publishNodeCompleted(nodeId, eventData) {
        try {
            const channel = enums_1.RedisChannelBuilder.buildNodeCompletedChannel(nodeId);
            await this.publishToRedis(channel, eventData);
            this.logger.debug(`Published NODE_COMPLETED event to ${channel}`);
        }
        catch (error) {
            this.logger.error('Failed to publish NODE_COMPLETED event:', error);
        }
    }
    async publishNodeFailed(nodeId, eventData) {
        try {
            const channel = enums_1.RedisChannelBuilder.buildNodeFailedChannel(nodeId);
            await this.publishToRedis(channel, eventData);
            this.logger.debug(`Published NODE_FAILED event to ${channel}`);
        }
        catch (error) {
            this.logger.error('Failed to publish NODE_FAILED event:', error);
        }
    }
    async publishWorkflowCompleted(workflowId, eventData) {
        try {
            const channel = enums_1.RedisChannelBuilder.buildWorkflowCompletedChannel(workflowId);
            await this.publishToRedis(channel, eventData);
            this.logger.debug(`Published WORKFLOW_COMPLETED event to ${channel}`);
        }
        catch (error) {
            this.logger.error('Failed to publish WORKFLOW_COMPLETED event:', error);
        }
    }
    async publishWorkflowFailed(workflowId, eventData) {
        try {
            const channel = enums_1.RedisChannelBuilder.buildWorkflowFailedChannel(workflowId);
            await this.publishToRedis(channel, eventData);
            this.logger.debug(`Published WORKFLOW_FAILED event to ${channel}`);
        }
        catch (error) {
            this.logger.error('Failed to publish WORKFLOW_FAILED event:', error);
        }
    }
    async publishToRedis(channel, data) {
        try {
            const message = typeof data === 'string' ? data : JSON.stringify(data);
            await (0, rxjs_1.firstValueFrom)(this.redisClient.emit(channel, message));
        }
        catch (error) {
            this.logger.error(`Failed to publish to Redis channel ${channel}:`, error);
            throw error;
        }
    }
    async getRedisHealth() {
        try {
            await (0, rxjs_1.firstValueFrom)(this.redisClient.send({ cmd: 'ping' }, {}));
            return {
                status: 'healthy',
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error('Redis health check failed:', error);
            return {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
            };
        }
    }
};
exports.WorkflowRedisService = WorkflowRedisService;
exports.WorkflowRedisService = WorkflowRedisService = WorkflowRedisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('REDIS_CLIENT')),
    __metadata("design:paramtypes", [microservices_1.ClientProxy,
        workflow_sse_user_service_1.UserWorkflowSSEService])
], WorkflowRedisService);
