/**
 * Enum cho các loại job trong BullMQ workflow queue
 */
export enum WorkflowJobType {
  /**
   * Job thực thi workflow của user (asynchronous via queue)
   */
  USER_EXECUTE = 'user_execute',

  /**
   * Job thực thi node cụ thể của user (synchronous via microservice)
   */
  USER_EXECUTE_NODE = 'user_execute_node',

  /**
   * Job thực thi của admin
   */
  ADMIN_EXECUTE = 'admin_execute',
}

/**
 * Type guard để kiểm tra job type có hợp lệ không
 */
export function isValidWorkflowJobType(jobType: string): jobType is WorkflowJobType {
  return Object.values(WorkflowJobType).includes(jobType as WorkflowJobType);
}

/**
 * Utility function để lấy tất cả job types
 */
export function getAllWorkflowJobTypes(): WorkflowJobType[] {
  return Object.values(WorkflowJobType);
}
