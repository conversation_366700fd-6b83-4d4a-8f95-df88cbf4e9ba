"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Execution = void 0;
const typeorm_1 = require("typeorm");
const execution_status_enum_1 = require("../enums/execution-status.enum");
let Execution = class Execution {
    id;
    workflowId;
    status;
    startedAt;
    finishedAt;
    errorMessage;
};
exports.Execution = Execution;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Execution.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'workflow_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], Execution.prototype, "workflowId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: execution_status_enum_1.ExecutionStatusEnum,
        default: execution_status_enum_1.ExecutionStatusEnum.SUCCEEDED,
        nullable: false,
    }),
    __metadata("design:type", String)
], Execution.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'started_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], Execution.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'finished_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], Execution.prototype, "finishedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_message', type: 'text', nullable: true }),
    __metadata("design:type", String)
], Execution.prototype, "errorMessage", void 0);
exports.Execution = Execution = __decorate([
    (0, typeorm_1.Entity)('executions')
], Execution);
