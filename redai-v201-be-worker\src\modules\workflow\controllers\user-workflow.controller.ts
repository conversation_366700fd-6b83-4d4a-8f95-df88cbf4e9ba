import { UserWorkflowService } from './../service/user-workflow.service';
import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { IExecutionPayload, NodeResponseDto } from '../dto/node-execute.dto';
import { WorkflowJobType } from '../enums/workflow-job-types.enum';
import { RedisPublisherService } from '../services/redis-publisher.service';

@Controller()
export class UserWorkflowController {
    private readonly logger = new Logger(UserWorkflowController.name);

    constructor(
        private readonly userWorkflowService: UserWorkflowService,
        private readonly redisPublisher: RedisPublisherService
    ) { }

    /**
     * Handle workflow/node execution - Unified handler
     * Logic:
     * - currentNode exists → Execute single node
     * - currentNode null/undefined → Execute full workflow
     * - Always REALTIME mode (push Redis events)
     */
    @MessagePattern(WorkflowJobType.USER_EXECUTE)
    async handleExecute(@Payload() data: IExecutionPayload): Promise<NodeResponseDto> {
        const isNodeExecution = !!data.currentNode;
        const executionType = isNodeExecution ? 'Single Node' : 'Full Workflow';

        this.logger.log(`[REALTIME] Handling ${executionType} execution: ${data.currentNode || 'entire workflow'}`);

        try {
            let result: any;

            if (isNodeExecution) {
                // Execute single node
                result = await this.userWorkflowService.executeNodeWithMode(
                    data,
                    'REALTIME'
                );

                // Push node completed event
                if (data.workflowId && data.userId && data.currentNode) {
                    this.logger.log(`[REALTIME] Publishing node completed event`);
                    await this.redisPublisher.publishNodeCompleted(
                        data.workflowId,
                        data.currentNode,
                        data.userId,
                        data.executionId,
                        result,
                        { mode: 'realtime', executionType: 'REALTIME' }
                    );
                }
            } else {
                // Execute full workflow
                result = await this.userWorkflowService.executeWorkflowWithMode(
                    data,
                    'REALTIME'
                );

                // Push workflow completed event
                if (data.workflowId && data.userId) {
                    this.logger.log(`[REALTIME] Publishing workflow completed event`);
                    await this.redisPublisher.publishExecutionCompleted(
                        data.executionId,
                        data.userId,
                        result,
                        { mode: 'realtime', executionType: 'REALTIME' }
                    );
                }
            }

            return { result };
        } catch (error) {
            this.logger.error(`[REALTIME] ${executionType} execution failed:`, error);

            // Push error event
            if (data.workflowId && data.userId) {
                if (isNodeExecution && data.currentNode) {
                    await this.redisPublisher.publishNodeFailed(
                        data.workflowId,
                        data.currentNode,
                        data.userId,
                        data.executionId,
                        error,
                        { mode: 'realtime', executionType: 'REALTIME' }
                    );
                } else {
                    await this.redisPublisher.publishError(
                        data.executionId,
                        data.userId,
                        error,
                        'execution'
                    );
                }
            }

            return {
                result: {
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                    executionType
                }
            };
        }
    }


}