import { UserWorkflowService } from './../service/user-workflow.service';
import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { IExecutionPayload, NodeResponseDto } from '../dto/node-execute.dto';
import { WorkflowJobType } from '../enums/workflow-job-types.enum';
import { RedisPublisherService } from '../services/redis-publisher.service';

@Controller()
export class UserWorkflowController {
    private readonly logger = new Logger(UserWorkflowController.name);

    constructor(
        private readonly userWorkflowService: UserWorkflowService,
        private readonly redisPublisher: RedisPublisherService
    ) { }

    /**
     * Handle node execution - REALTIME execution only
     * Đây là luồng đồng bộ (synchronous) được gọi trực tiếp từ BE app
     * Luôn luôn push events về Redis để user thấy realtime updates
     */
    @MessagePattern(WorkflowJobType.USER_EXECUTE)
    async handleExecuteNode(@Payload() data: IExecutionPayload): Promise<NodeResponseDto> {
        this.logger.log(`[REALTIME] Handling node execution: ${data.currentNode}`);

        try {
            // Thực thi node với mode REALTIME
            const result = await this.userWorkflowService.executeNodeWithMode(
                data,
                'REALTIME' // Luôn luôn là REALTIME cho controller
            );

            // Luôn luôn push event về Redis cho realtime execution
            if (data.workflowId && data.userId && data.currentNode) {
                this.logger.log(`[REALTIME] Publishing node completed event`);
                await this.redisPublisher.publishNodeCompleted(
                    data.workflowId,
                    data.currentNode,
                    data.userId,
                    data.executionId,
                    result,
                    { mode: 'realtime', executionType: 'REALTIME' }
                );
            }

            return { result };
        } catch (error) {
            this.logger.error(`[REALTIME] Node execution failed:`, error);

            // Luôn luôn publish error event cho realtime execution
            if (data.workflowId && data.userId && data.currentNode) {
                await this.redisPublisher.publishNodeFailed(
                    data.workflowId,
                    data.currentNode,
                    data.userId,
                    data.executionId,
                    error,
                    { mode: 'realtime', executionType: 'REALTIME' }
                );
            }

            return {
                result: {
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                }
            };
        }
    }


}