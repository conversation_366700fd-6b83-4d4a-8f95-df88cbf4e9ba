import { UserWorkflowService } from './../service/user-workflow.service';
import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { IExecutionPayload } from '../dto/node-execute.dto';

@Controller()
export class UserWorkflowController {
    private readonly logger = new Logger(UserWorkflowController.name);

    constructor(private readonly userWorkflowService: UserWorkflowService) { }

    /**
     * Handle node execution - test hoặc execute một node cụ thể
     */
    @MessagePattern(WorkflowJobType.USER_EXECUTE_NODE)
    async handleExecuteNode(@Payload() data: IExecutionPayload): Promise<NodeResponseDto> {

        try {
            // Kiểm tra type để xác định mode execution
            if (data.type === 'test') {
                // Test mode - chỉ test node mà không lưu vào database chính
                const result = await this.userWorkflowService.testNode(data);
                return { result };
            } else if (data.type === 'execute') {
                // Execute mode - thực thi node v<PERSON> l<PERSON>u kết quả
                const result = await this.userWorkflowService.executeNode(data);
                return { result };
            } else {
                throw new Error(`Invalid execution type: ${data.type}. Must be 'test' or 'execute'`);
            }
        } catch (error) {
            this.logger.error(`Node execution failed:`, error);
            return {
                result: {
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                }
            };
        }
    }
}