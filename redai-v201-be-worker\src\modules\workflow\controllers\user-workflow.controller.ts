import { UserWorkflowService } from './../service/user-workflow.service';
import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { IExecutionPayload, NodeResponseDto } from '../dto/node-execute.dto';
import { WorkflowJobType } from '../enums/workflow-job-types.enum';
import { RedisPublisherService } from '../services/redis-publisher.service';

@Controller()
export class UserWorkflowController {
    private readonly logger = new Logger(UserWorkflowController.name);

    constructor(
        private readonly userWorkflowService: UserWorkflowService,
        private readonly redisPublisher: RedisPublisherService
    ) { }

    /**
     * Handle node execution - test hoặc execute một node cụ thể
     * Đây là luồng đồng bộ (synchronous) được gọi trực tiếp từ BE app
     */
    @MessagePattern(WorkflowJobType.USER_EXECUTE_NODE)
    async handleExecuteNode(@Payload() data: IExecutionPayload): Promise<NodeResponseDto> {
        this.logger.log(`[SYNC] Handling node execution: ${data.currentNode} - Type: ${data.type}`);

        try {
            // Extract execution config từ inputData để xác định có cần push event không
            const executionConfig = data.inputData?._execution || {
                mode: 'background',
                userOnline: false
            };

            let result: any;

            // Kiểm tra type để xác định mode execution
            if (data.type === 'test') {
                // Test mode - chỉ test node mà không lưu vào database chính
                this.logger.log(`[SYNC] Test mode execution for node: ${data.currentNode}`);
                result = await this.userWorkflowService.testNode(data);
            } else if (data.type === 'execute') {
                // Execute mode - thực thi node và lưu kết quả
                this.logger.log(`[SYNC] Execute mode execution for node: ${data.currentNode} - Mode: ${executionConfig.mode}`);
                result = await this.userWorkflowService.executeNode(data);
            } else {
                throw new Error(`Invalid execution type: ${data.type}. Must be 'test' or 'execute'`);
            }

            // Chỉ push event về Redis nếu cần thiết (realtime mode hoặc test mode)
            const shouldPublish = this.shouldPublishEvents(data.type, executionConfig);
            if (shouldPublish && data.workflowId && data.userId && data.currentNode) {
                this.logger.log(`[SYNC] Publishing node completed event - Mode: ${executionConfig.mode}`);
                await this.redisPublisher.publishNodeCompleted(
                    data.workflowId,
                    data.currentNode,
                    data.userId,
                    data.executionId,
                    result,
                    { ...executionConfig, type: data.type }
                );
            } else {
                this.logger.log(`[SYNC] Skipping event publish - Mode: ${executionConfig.mode}, Type: ${data.type}`);
            }

            return { result };
        } catch (error) {
            this.logger.error(`[SYNC] Node execution failed:`, error);

            // Publish error event nếu cần thiết
            const executionConfig = data.inputData?._execution || { mode: 'background' };
            const shouldPublish = this.shouldPublishEvents(data.type, executionConfig);
            if (shouldPublish && data.workflowId && data.userId && data.currentNode) {
                await this.redisPublisher.publishNodeFailed(
                    data.workflowId,
                    data.currentNode,
                    data.userId,
                    data.executionId,
                    error,
                    { ...executionConfig, type: data.type }
                );
            }

            return {
                result: {
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                }
            };
        }
    }

    /**
     * Kiểm tra có nên publish event về Redis không
     * Logic giống với RedisPublisherService.shouldPublishEvents
     */
    private shouldPublishEvents(type?: string, executionConfig?: any): boolean {
        // Always publish for test mode
        if (type === 'test') {
            return true;
        }

        // For execute mode, check execution mode
        if (executionConfig?.mode === 'realtime') {
            return true;
        }

        // For background mode, don't publish events
        if (executionConfig?.mode === 'background') {
            this.logger.debug('[SYNC] Skipping event publish for background execution');
            return false;
        }

        // Default: publish events (backward compatibility)
        return true;
    }
}