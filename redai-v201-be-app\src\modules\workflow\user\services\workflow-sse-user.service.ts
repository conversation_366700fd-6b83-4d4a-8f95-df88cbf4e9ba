import { Injectable, Logger } from '@nestjs/common';
import { Response } from 'express';
import { AllSSEEvents, WorkflowNodeEvent } from '../../interfaces';

export interface WorkflowSSEClient {
  id: string;
  userId: number;
  response: Response,
  workflowId?: string;
  nodeId?: string;
  lastPing: Date;
}

/**
 * Service quản lý Server-Sent Events cho workflow execution (User)
 * Gửi real-time updates về trạng thái thực hiện workflow và node cho user
 * Tích hợp với Redis để nhận events từ BE worker
 */
@Injectable()
export class UserWorkflowSSEService {
  private readonly logger = new Logger(UserWorkflowSSEService.name);
  private readonly clients = new Map<string, WorkflowSSEClient>();
  private readonly pingInterval = 30000; // 30 seconds
  private pingTimer: NodeJS.Timeout;

  constructor(
  ) {
    this.startPingTimer();
  }

  // ========== CONNECTION MANAGEMENT ==========

  /**
   * Tạo SSE connection cho user
   */
  createSSEConnection(
    userId: number,
    response: Response,
    nodeId?: string,
    workflowId?: string,
  ): string {
    const clientId = this.generateClientId();

    // Setup SSE headers
    response.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // Tạo client object
    const client: WorkflowSSEClient = {
      id: clientId,
      userId,
      workflowId,
      nodeId,
      response,
      lastPing: new Date(),
    };

    // Lưu client
    this.clients.set(clientId, client);

    // Gửi connection established event
    this.sendConnectionEstablished(client);

    // Setup cleanup khi client disconnect
    response.on('close', () => {
      this.logger.log(`🔌 Client disconnected: ${clientId} (User: ${userId})`);
      this.removeClient(clientId);
    });

    response.on('error', (error) => {
      this.logger.error(`🔥 SSE Response error for client ${clientId}:`, error);
      this.removeClient(clientId);
    });

    this.logger.log(`✅ SSE connection created: ${clientId} (User: ${userId}, Workflow: ${workflowId})`);

    return clientId;
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `sse_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Gửi connection established event đến client
   */
  private sendConnectionEstablished(client: WorkflowSSEClient): void {
    try {
      const event = {
        type: 'connection.established',
        data: {
          clientId: client.id,
          userId: client.userId,
          workflowId: client.workflowId,
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      };

      const sseData = `data: ${JSON.stringify(event)}\n\n`;
      client.response.write(sseData);
      client.lastPing = new Date();
    } catch (error) {
      this.logger.error(`Failed to send connection established to client ${client.id}:`, error);
    }
  }

  /**
   * Start ping timer để maintain connections
   */
  private startPingTimer(): void {
    this.pingTimer = setInterval(() => {
      this.sendPingToAllClients();
    }, this.pingInterval);
  }

  /**
   * Gửi ping đến tất cả clients
   */
  private sendPingToAllClients(): void {
    const now = new Date();
    const clientsToRemove: string[] = [];

    this.clients.forEach((client, clientId) => {
      try {
        // Check if client is stale (no response for 2 ping intervals)
        const timeSinceLastPing = now.getTime() - client.lastPing.getTime();
        if (timeSinceLastPing > this.pingInterval * 2) {
          this.logger.warn(`Client ${clientId} is stale, removing...`);
          clientsToRemove.push(clientId);
          return;
        }

        // Send ping
        const pingEvent = {
          type: 'ping',
          timestamp: now.toISOString(),
        };

        const sseData = `data: ${JSON.stringify(pingEvent)}\n\n`;
        client.response.write(sseData);

      } catch (error) {
        this.logger.error(`Failed to ping client ${clientId}:`, error);
        clientsToRemove.push(clientId);
      }
    });

    // Remove stale clients
    clientsToRemove.forEach(clientId => {
      this.removeClient(clientId);
    });

    if (this.clients.size > 0) {
      this.logger.debug(`Pinged ${this.clients.size} SSE clients`);
    }
  }

  // ========== UTILITY METHODS ==========

  /**
   * Check if user is online (has active SSE connections)
   */
  isUserOnline(userId: number): boolean {
    const userClients = Array.from(this.clients.values()).filter(
      client => client.userId === userId
    );
    return userClients.length > 0;
  }

  /**
   * Get client by ID
   */
  getClient(clientId: string): WorkflowSSEClient | undefined {
    return this.clients.get(clientId);
  }

  /**
   * Get all clients
   */
  getAllClients(): WorkflowSSEClient[] {
    return Array.from(this.clients.values());
  }

  /**
   * Get clients count
   */
  getClientsCount(): number {
    return this.clients.size;
  }

  /**
   * Get clients by user ID
   */
  getClientsByUser(userId: number): WorkflowSSEClient[] {
    return Array.from(this.clients.values()).filter(
      client => client.userId === userId
    );
  }

  /**
   * Get clients by workflow ID
   */
  getClientsByWorkflow(workflowId: string): WorkflowSSEClient[] {
    return Array.from(this.clients.values()).filter(
      client => client.workflowId === workflowId
    );
  }

  /**
   * Send custom event to specific client
   */
  sendToClient(clientId: string, event: AllSSEEvents): boolean {
    const client = this.clients.get(clientId);
    if (!client) {
      this.logger.warn(`Client ${clientId} not found`);
      return false;
    }

    try {
      const sseData = `data: ${JSON.stringify(event)}\n\n`;
      client.response.write(sseData);
      client.lastPing = new Date();
      return true;
    } catch (error) {
      this.logger.error(`Failed to send event to client ${clientId}:`, error);
      this.removeClient(clientId);
      return false;
    }
  }

  /**
   * Send event to all clients of a user
   */
  sendToUser(userId: number, event: AllSSEEvents): number {
    const userClients = this.getClientsByUser(userId);
    let sentCount = 0;

    userClients.forEach(client => {
      if (this.sendToClient(client.id, event)) {
        sentCount++;
      }
    });

    return sentCount;
  }

  /**
   * Send event to all clients of a workflow
   */
  sendToWorkflow(workflowId: string, event: WorkflowNodeEvent): number {
    const workflowClients = this.getClientsByWorkflow(workflowId);
    let sentCount = 0;

    workflowClients.forEach(client => {
      if (this.sendToClient(client.id, event)) {
        sentCount++;
      }
    });

    return sentCount;
  }

  /**
   * Get all connection IDs for a user
   */
  getUserConnections(userId: number): string[] {
    return Array.from(this.clients.values())
      .filter(client => client.userId === userId)
      .map(client => client.id);
  }

  /**
   * Get all connection IDs for a workflow
   */
  getWorkflowConnections(workflowId: string): string[] {
    return Array.from(this.clients.values())
      .filter(client => client.workflowId === workflowId)
      .map(client => client.id);
  }

  /**
   * Remove a client connection
   */
  removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      try {
        client.response.end();
      } catch (error) {
        this.logger.warn(`Error closing client ${clientId} response:`, error);
      }
      this.clients.delete(clientId);
      this.logger.debug(`Client ${clientId} removed`);
    }
  }

  /**
   * Cleanup khi service bị destroy
   */
  onModuleDestroy(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
    }

    // Close tất cả connections
    this.clients.forEach((_, clientId) => {
      this.removeClient(clientId);
    });
  }
}
