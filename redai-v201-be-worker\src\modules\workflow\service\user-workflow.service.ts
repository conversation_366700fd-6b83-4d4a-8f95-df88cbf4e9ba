import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { UserNodeExecuteDto } from '../dto/node-execute.dto';
import { ExecutionRepository } from '../repositories/execution.repository';
import { ExecutionNodeDataRepository } from '../repositories/execution-node-data.repository';
import { WorkflowRepository } from '../repositories/workflow.repository';
import { NodeRepository } from '../repositories/node.repository';
import { ConnectionRepository } from '../repositories/connection.repository';
import { NodeDefinitionRepository } from '../repositories/node-definition.repository';
import { WorkflowXStateService } from '../services/xstate/services/workflow-xstate.service';
import { MachineIntegrationService } from '../services/xstate/machines/machine-integration.service';
import { RedisPublisherService } from '../services/redis-publisher.service';
import { ExecutionStatusEnum } from '../enums/execution-status.enum';

@Injectable()
export class UserWorkflowService {
    private readonly logger = new Logger(UserWorkflowService.name);

    constructor(
        private readonly executionRepository: ExecutionRepository,
        private readonly executionNodeDataRepository: ExecutionNodeDataRepository,
        private readonly workflowRepository: WorkflowRepository,
        private readonly nodeRepository: NodeRepository,
        private readonly connectionRepository: ConnectionRepository,
        private readonly nodeDefinitionRepository: NodeDefinitionRepository,
        private readonly workflowXStateService: WorkflowXStateService,
        private readonly machineIntegrationService: MachineIntegrationService,
        private readonly redisPublisher: RedisPublisherService,
    ) {}

    /**
     * Test một node cụ thể - không lưu vào database chính
     */
    async testNode(data: UserNodeExecuteDto): Promise<any> {
        const { userId, workflowId, nodeId, type, inputData } = data;

        this.logger.log(`Testing node: ${nodeId} in workflow: ${workflowId} for user: ${userId}`);

        // Generate test execution ID outside try block to ensure it's accessible in catch
        const testExecutionId = `test_${uuidv4()}`;

        try {
            // Publish test started event
            await this.redisPublisher.publishTestStarted(testExecutionId, userId, {
                workflowId,
                nodeId,
                type,
                mode: 'single_node'
            });

            // Execute node trong test mode
            const result = await this.executeNodeInternal(
                testExecutionId,
                workflowId,
                nodeId || 'unknown_node',
                userId,
                true, // isTestMode
                inputData // Pass inputData to execution
            );

            // Publish test completed event
            await this.redisPublisher.publishTestCompleted(testExecutionId, userId, {
                success: result.success,
                output: result.output,
                error: result.error,
                executionTime: result.executionTime || 0,
                nodesExecuted: 1,
                totalNodes: 1
            });

            this.logger.log(`Node test completed: ${nodeId}`);
            return { ...result, testId: testExecutionId };

        } catch (error) {
            this.logger.error(`Node test failed: ${nodeId}`, error);

            // Publish error event - testExecutionId is now accessible
            await this.redisPublisher.publishError(testExecutionId, userId, error, 'test');

            throw error;
        }
    }

    /**
     * Execute một node cụ thể - lưu vào database
     */
    async executeNode(data: UserNodeExecuteDto): Promise<any> {
        const { userId, workflowId, nodeId, type, inputData } = data;

        this.logger.log(`Executing node: ${nodeId} in workflow: ${workflowId} for user: ${userId}`);

        try {
            // Generate execution ID
            const executionId = uuidv4();

            // Create execution record
            await this.executionRepository.create({
                id: executionId,
                workflowId,
                status: ExecutionStatusEnum.RUNNING,
                startedAt: Date.now(),
            });

            // Execute node
            const result = await this.executeNodeInternal(
                executionId,
                workflowId,
                nodeId || 'unknown_node',
                userId,
                false, // isTestMode
                inputData // Pass inputData to execution
            );

            // Update execution status
            await this.executionRepository.updateExecutionStatus(
                executionId,
                result.success ? 'completed' : 'failed'
            );

            this.logger.log(`Node execution completed: ${nodeId}`);
            return { ...result, executionId };

        } catch (error) {
            this.logger.error(`Node execution failed: ${nodeId}`, error);
            throw error;
        }
    }

    /**
     * Execute node với execution mode cụ thể (REALTIME hoặc BACKGROUND)
     */
    async executeNodeWithMode(data: IExecutionPayload, executionMode: 'REALTIME' | 'BACKGROUND'): Promise<any> {
        const { userId, workflowId, currentNode, executionId } = data;

        this.logger.log(`[${executionMode}] Executing node: ${currentNode} in workflow: ${workflowId} for user: ${userId}`);

        try {
            // Execute node với execution mode
            const result = await this.executeNodeInternal(
                executionId,
                workflowId,
                currentNode || 'unknown_node',
                userId,
                false, // isTestMode
                data.initContext, // Pass initContext as inputData
                executionMode // Pass execution mode
            );

            this.logger.log(`[${executionMode}] Node execution completed: ${currentNode}`);
            return { ...result, executionId, executionMode };

        } catch (error) {
            this.logger.error(`[${executionMode}] Node execution failed: ${currentNode}`, error);
            throw error;
        }
    }

    /**
     * Test toàn bộ workflow - không lưu vào database chính
     */
    async testWorkflow(data: UserNodeExecuteDto): Promise<any> {
        const { userId, workflowId, type, inputData } = data;

        this.logger.log(`Testing workflow: ${workflowId} for user: ${userId}`);

        // Generate test execution ID outside try block to ensure it's accessible in catch
        const testExecutionId = `test_workflow_${uuidv4()}`;

        try {
            // Publish test started event
            await this.redisPublisher.publishTestStarted(testExecutionId, userId, {
                workflowId,
                type,
                mode: 'full_workflow'
            });

            // Execute workflow trong test mode
            const result = await this.executeWorkflowInternal(
                testExecutionId,
                workflowId,
                userId,
                true, // isTestMode
                inputData // Pass inputData for trigger context
            );

            // Publish test completed event
            await this.redisPublisher.publishTestCompleted(testExecutionId, userId, {
                success: result.success,
                output: result.output,
                error: result.error,
                executionTime: result.executionTime || 0,
                nodesExecuted: result.nodesExecuted || 0,
                totalNodes: result.totalNodes || 0
            });

            this.logger.log(`Workflow test completed: ${workflowId}`);
            return { ...result, testId: testExecutionId };

        } catch (error) {
            this.logger.error(`Workflow test failed: ${workflowId}`, error);

            // Publish error event - testExecutionId is now accessible
            await this.redisPublisher.publishError(testExecutionId, userId, error, 'test');

            throw error;
        }
    }

    /**
     * Execute toàn bộ workflow - lưu vào database
     */
    async executeWorkflow(data: UserNodeExecuteDto): Promise<any> {
        const { userId, workflowId, type, inputData } = data;

        this.logger.log(`Executing workflow: ${workflowId} for user: ${userId}`);

        // Generate execution ID outside try block to ensure it's accessible in catch
        const executionId = uuidv4();

        try {
            // Create execution record
            await this.executionRepository.create({
                id: executionId,
                workflowId,
                status: ExecutionStatusEnum.RUNNING,
                startedAt: Date.now(),
            });

            // Publish execution started event
            await this.redisPublisher.publishExecutionStarted(executionId, userId, {
                workflowId,
                type,
                mode: 'full_workflow'
            });

            // Execute workflow
            const result = await this.executeWorkflowInternal(
                executionId,
                workflowId,
                userId,
                false, // isTestMode
                inputData // Pass inputData for trigger context
            );

            // Update execution status
            await this.executionRepository.updateExecutionStatus(
                executionId,
                result.success ? 'completed' : 'failed'
            );

            // Publish execution completed event
            await this.redisPublisher.publishExecutionCompleted(executionId, userId, result);

            this.logger.log(`Workflow execution completed: ${workflowId}`);
            return { ...result, executionId };

        } catch (error) {
            this.logger.error(`Workflow execution failed: ${workflowId}`, error);

            // Publish error event - executionId is now accessible
            await this.redisPublisher.publishError(executionId, userId, error, 'execution');

            throw error;
        }
    }

    /**
     * Internal method để execute một node cụ thể
     */
    private async executeNodeInternal(
        executionId: string,
        workflowId: string,
        nodeId: string,
        userId: number,
        isTestMode: boolean,
        inputData?: Record<string, any>
    ): Promise<any> {
        this.logger.log(`Executing node internal: ${nodeId}, test mode: ${isTestMode}, with inputData: ${!!inputData}`);

        // Load node information để get name (outside try block để accessible trong catch)
        const nodeEntity = await this.nodeRepository.findById(nodeId);
        const nodeName = nodeEntity?.name || `Node ${nodeId}`;

        try {
            // Publish progress update
            await this.redisPublisher.publishTestProgress(executionId, userId, {
                currentStep: 1,
                totalSteps: 1,
                percentage: 0,
                description: `Starting node execution: ${nodeId}`,
                nodeId
            });

            // Extract context from inputData if provided
            const executionContext = this.extractExecutionContext(inputData);

            // Mock node execution - trong thực tế sẽ sử dụng XState machine
            const startTime = Date.now();

            // Simulate node execution with context
            await new Promise(resolve => setTimeout(resolve, 1000));

            const executionTime = Date.now() - startTime;
            const result = {
                success: true,
                output: {
                    nodeId,
                    message: `Node ${nodeId} executed successfully`,
                    timestamp: Date.now(),
                    isTestMode,
                    // Include input context in output
                    inputContext: executionContext,
                    processedData: this.processNodeWithContext(nodeEntity, executionContext)
                },
                executionTime,
                metadata: {
                    nodeId,
                    executionId,
                    workflowId,
                    userId,
                    isTestMode,
                    hasInputData: !!inputData,
                    contextKeys: Object.keys(executionContext)
                }
            };

            // Publish progress completion
            await this.redisPublisher.publishTestProgress(executionId, userId, {
                currentStep: 1,
                totalSteps: 1,
                percentage: 100,
                description: `Node execution completed: ${nodeId}`,
                nodeId,
                nodeResult: result
            });

            // Save node execution data if not test mode
            if (!isTestMode) {
                await this.executionNodeDataRepository.create({
                    executionId,
                    nodeName: nodeName,
                    inputData: { nodeId, workflowId },
                    outputData: result.output,
                    executedAt: Date.now(),
                });
            }

            return result;

        } catch (error) {
            this.logger.error(`Node execution internal failed: ${nodeId}`, error);

            const errorResult = {
                success: false,
                error: {
                    message: error.message,
                    code: 'NODE_EXECUTION_ERROR',
                    nodeId,
                    executionId
                },
                executionTime: Date.now() - Date.now()
            };

            // Save error if not test mode
            if (!isTestMode) {
                await this.executionNodeDataRepository.create({
                    executionId,
                    nodeName: nodeName,
                    inputData: { nodeId, workflowId },
                    outputData: { error: error.message },
                    executedAt: Date.now(),
                });
            }

            return errorResult;
        }
    }

    /**
     * Internal method để execute toàn bộ workflow sử dụng XState
     */
    private async executeWorkflowInternal(
        executionId: string,
        workflowId: string,
        userId: number,
        isTestMode: boolean,
        inputData?: Record<string, any>
    ): Promise<any> {
        this.logger.log(`Executing workflow internal with XState: ${workflowId}, test mode: ${isTestMode}, with inputData: ${!!inputData}`);

        try {
            const startTime = Date.now();

            // Extract trigger context from inputData
            const triggerContext = this.extractExecutionContext(inputData);

            // 1. Load workflow definition từ database
            const workflowDefinition = await this.loadWorkflowDefinition(workflowId);

            // 1.5. Validate workflow definition
            this.validateWorkflowDefinition(workflowDefinition);

            // 2. Create WorkflowContext cho XState machine với trigger context
            const workflowContext = await this.createWorkflowContext(
                executionId,
                workflowId,
                userId,
                workflowDefinition,
                isTestMode,
                triggerContext
            );

            // 3. Publish workflow started
            if (isTestMode) {
                await this.redisPublisher.publishTestStarted(executionId, userId, {
                    workflowId,
                    mode: 'xstate_workflow',
                    totalNodes: workflowContext.metadata?.totalNodes || 0
                });
            } else {
                await this.redisPublisher.publishExecutionStarted(executionId, userId, {
                    workflowId,
                    mode: 'xstate_workflow',
                    totalNodes: workflowContext.metadata?.totalNodes || 0
                });
            }

            // 4. Start XState workflow machine
            const workflowActor = await this.machineIntegrationService.startWorkflow(workflowContext);

            // 5. Wait for workflow completion
            const result = await this.waitForWorkflowCompletion(workflowActor, executionId, userId, isTestMode);

            const executionTime = Date.now() - startTime;

            return {
                ...result,
                executionTime,
                metadata: {
                    executionId,
                    workflowId,
                    userId,
                    isTestMode,
                    usedXState: true
                }
            };

        } catch (error) {
            this.logger.error(`XState workflow execution failed: ${workflowId}`, error);

            // Publish error
            const errorType = isTestMode ? 'test' : 'execution';
            await this.redisPublisher.publishError(executionId, userId, error, errorType);

            return {
                success: false,
                error: {
                    message: error.message,
                    code: 'XSTATE_WORKFLOW_ERROR',
                    workflowId,
                    executionId
                },
                executionTime: Date.now() - Date.now(),
                nodesExecuted: 0,
                totalNodes: 0
            };
        }
    }

    /**
     * Load workflow definition từ database
     */
    private async loadWorkflowDefinition(workflowId: string): Promise<any> {
        this.logger.debug(`Loading workflow definition for: ${workflowId}`);

        try {
            // 1. Load workflow metadata
            const workflow = await this.workflowRepository.findById(workflowId);
            if (!workflow) {
                throw new Error(`Workflow not found: ${workflowId}`);
            }

            if (!workflow.isActive) {
                throw new Error(`Workflow is not active: ${workflowId}`);
            }

            // 2. Load all nodes for this workflow
            const nodes = await this.nodeRepository.findByWorkflowId(workflowId);
            if (nodes.length === 0) {
                throw new Error(`No nodes found for workflow: ${workflowId}`);
            }

            // 3. Load all connections for this workflow
            const connections = await this.connectionRepository.findByWorkflowId(workflowId);

            // 4. Load node definitions for each node to get type information
            const nodeDefinitions = new Map();
            for (const node of nodes) {
                if (node.nodeDefinitionId) {
                    const nodeDefinition = await this.nodeDefinitionRepository.findById(node.nodeDefinitionId);
                    if (nodeDefinition) {
                        nodeDefinitions.set(node.id, nodeDefinition);
                    }
                }
            }

            // 5. Build dependency graph from connections
            const dependencyGraph = this.buildDependencyGraph(nodes, connections);

            // 6. Identify trigger nodes (nodes without dependencies)
            const triggerNodes = nodes.filter(node => {
                const dependencies = dependencyGraph.get(node.id) || [];
                return dependencies.length === 0;
            });

            // 7. Identify processing nodes (nodes with dependencies)
            const processingNodes = nodes.filter(node => {
                const dependencies = dependencyGraph.get(node.id) || [];
                return dependencies.length > 0;
            });

            this.logger.debug(`Loaded workflow: ${workflow.name}, Nodes: ${nodes.length}, Connections: ${connections.length}`);
            this.logger.debug(`Trigger nodes: ${triggerNodes.length}, Processing nodes: ${processingNodes.length}`);

            return {
                id: workflow.id,
                name: workflow.name,
                isActive: workflow.isActive,
                settings: workflow.settings,
                nodes: nodes.map(node => ({
                    id: node.id,
                    name: node.name,
                    workflowId: node.workflowId,
                    position: node.position,
                    parameters: node.parameters,
                    disabled: node.disabled,
                    notes: node.notes,
                    retryOnFail: node.retryOnFail,
                    maxTries: node.maxTries,
                    waitBetweenTries: node.waitBetweenTries,
                    onError: node.onError,
                    agentId: node.agentId,
                    integrationId: node.integrationId,
                    nodeDefinitionId: node.nodeDefinitionId,
                    // Add computed fields
                    dependencies: dependencyGraph.get(node.id) || [],
                    nodeDefinition: nodeDefinitions.get(node.id),
                    isTriggerNode: triggerNodes.some(tn => tn.id === node.id),
                    isProcessingNode: processingNodes.some(pn => pn.id === node.id)
                })),
                connections: connections.map(conn => ({
                    id: conn.id,
                    workflowId: conn.workflowId,
                    from: conn.sourceNodeId,
                    to: conn.targetNodeId,
                    sourceHandle: conn.sourceHandle,
                    targetHandle: conn.targetHandle,
                    sourceHandleIndex: conn.sourceHandleIndex,
                    targetHandleIndex: conn.targetHandleIndex
                })),
                triggerNodes: triggerNodes.map(node => node.id),
                processingNodes: processingNodes.map(node => node.id),
                dependencyGraph,
                metadata: {
                    totalNodes: nodes.length,
                    totalConnections: connections.length,
                    triggerNodeCount: triggerNodes.length,
                    processingNodeCount: processingNodes.length,
                    loadedAt: Date.now()
                }
            };

        } catch (error) {
            this.logger.error(`Failed to load workflow definition: ${workflowId}`, error);
            throw error;
        }
    }

    /**
     * Create WorkflowContext cho XState machine
     */
    private async createWorkflowContext(
        executionId: string,
        workflowId: string,
        userId: number,
        workflowDefinition: any,
        isTestMode: boolean,
        triggerContext?: Record<string, any>
    ): Promise<any> {
        // Create nodes map
        const nodes = new Map();
        const dependencyGraph = new Map();

        // Process trigger node outputs - chỉ là outputs đơn giản từ BE App
        const triggerNodeOutputs = new Map();

        // Xử lý triggerNodeOutputs array
        if (triggerContext?.triggerNodeOutputs && Array.isArray(triggerContext.triggerNodeOutputs)) {
            for (const output of triggerContext.triggerNodeOutputs) {
                const nodeId = output.nodeId || `trigger_${Date.now()}`;
                triggerNodeOutputs.set(nodeId, {
                    nodeId,
                    outputData: output.outputData || output, // Flexible: có thể là object hoặc direct data
                    success: output.success !== false, // Default true
                    processedAt: output.processedAt || Date.now()
                });
            }
        }

        // Xử lý nodeOutputs object (alternative format)
        if (triggerContext?.nodeOutputs && typeof triggerContext.nodeOutputs === 'object') {
            for (const [nodeId, outputData] of Object.entries(triggerContext.nodeOutputs)) {
                triggerNodeOutputs.set(nodeId, {
                    nodeId,
                    outputData,
                    success: true,
                    processedAt: Date.now()
                });
            }
        }

        // Process workflow definition
        for (const node of workflowDefinition.nodes) {
            // Check if this is a trigger node with pre-computed output
            const triggerOutput = triggerNodeOutputs.get(node.id);
            const isTriggerNode = node.isTriggerNode || triggerOutput;

            nodes.set(node.id, {
                id: node.id,
                status: isTriggerNode ? 'completed' : 'pending', // Mark trigger nodes as completed
                inputData: isTriggerNode ? (triggerOutput?.triggerData || {}) : {},
                outputData: isTriggerNode ? (triggerOutput?.outputData || {}) : {},
                retryCount: 0,
                node: node,
                isTriggerNode,
                triggerOutput
            });

            dependencyGraph.set(node.id, node.dependencies || []);
        }

        // Determine initial ready nodes
        // For trigger context: processing nodes whose dependencies are satisfied by trigger outputs
        // For normal execution: nodes with no dependencies
        const readyNodes = this.determineReadyNodes(workflowDefinition.nodes, dependencyGraph, triggerNodeOutputs);

        const waitingNodes = workflowDefinition.nodes
            .filter((node: any) => node.dependencies && node.dependencies.length > 0)
            .map((node: any) => node.id);

        return {
            workflowId,
            executionId,
            nodes,
            connections: new Map(workflowDefinition.connections.map((c: any) => [c.from, c.to])),
            dependencyGraph,
            readyNodes,
            runningNodes: [],
            waitingNodes,
            executionData: new Map(),
            errors: new Map(),
            // Include trigger context
            triggerContext: triggerContext || {},
            triggerNodeOutputs,
            hasTriggerContext: !!triggerContext && Object.keys(triggerContext).length > 0,
            metadata: {
                startTime: Date.now(),
                userId,
                triggerType: 'manual', // Đơn giản hóa
                totalNodes: workflowDefinition.nodes.length,
                completedNodes: triggerNodeOutputs.size, // Số trigger nodes đã hoàn thành
                failedNodes: 0,
                processingNodes: workflowDefinition.nodes.length - triggerNodeOutputs.size,
                triggerNodes: triggerNodeOutputs.size,
                retryCount: 0,
                hasTriggerOutputs: triggerNodeOutputs.size > 0
            },
            options: {
                timeout: 3600000, // 1 hour
                maxConcurrency: 5,
                retryOnFailure: true
            },
            triggerData: {
                isTestMode,
                ...triggerContext?.triggerData
            }
        };
    }

    /**
     * Wait for workflow completion và handle events
     */
    private async waitForWorkflowCompletion(
        workflowActor: any,
        _executionId: string,
        _userId: number,
        _isTestMode: boolean
    ): Promise<any> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Workflow execution timeout'));
            }, 300000); // 5 minutes timeout

            // Subscribe to workflow events
            workflowActor.subscribe({
                next: (snapshot: any) => {
                    this.logger.debug(`Workflow state: ${JSON.stringify(snapshot.value)}`);

                    // Check if workflow completed
                    if (snapshot.status === 'done') {
                        clearTimeout(timeout);

                        const context = snapshot.context;
                        const result = {
                            success: context.result?.success || false,
                            output: context.result?.output || {},
                            totalNodes: context.metadata?.totalNodes || 0,
                            nodesExecuted: context.metadata?.completedNodes || 0,
                            failedNodes: context.metadata?.failedNodes || 0,
                            errors: Object.fromEntries(context.errors || new Map())
                        };

                        resolve(result);
                    }
                },
                error: (error: any) => {
                    clearTimeout(timeout);
                    reject(error);
                },
                complete: () => {
                    this.logger.debug('Workflow actor completed');
                }
            });
        });
    }

    /**
     * Build dependency graph từ connections
     */
    private buildDependencyGraph(nodes: any[], connections: any[]): Map<string, string[]> {
        const dependencyGraph = new Map<string, string[]>();

        // Initialize all nodes with empty dependencies
        nodes.forEach(node => {
            dependencyGraph.set(node.id, []);
        });

        // Build dependencies from connections
        connections.forEach(connection => {
            const targetNodeId = connection.targetNodeId || connection.to;
            const sourceNodeId = connection.sourceNodeId || connection.from;

            if (targetNodeId && sourceNodeId) {
                const dependencies = dependencyGraph.get(targetNodeId) || [];
                if (!dependencies.includes(sourceNodeId)) {
                    dependencies.push(sourceNodeId);
                    dependencyGraph.set(targetNodeId, dependencies);
                }
            }
        });

        return dependencyGraph;
    }

    /**
     * Validate workflow definition trước khi execute
     */
    private validateWorkflowDefinition(workflowDefinition: any): void {
        if (!workflowDefinition.nodes || workflowDefinition.nodes.length === 0) {
            throw new Error('Workflow must have at least one node');
        }

        if (workflowDefinition.triggerNodes.length === 0) {
            throw new Error('Workflow must have at least one trigger node');
        }

        // Check for circular dependencies
        this.detectCircularDependencies(workflowDefinition.dependencyGraph);
    }

    /**
     * Detect circular dependencies trong workflow
     */
    private detectCircularDependencies(dependencyGraph: Map<string, string[]>): void {
        const visited = new Set<string>();
        const recursionStack = new Set<string>();

        const hasCycle = (nodeId: string): boolean => {
            if (recursionStack.has(nodeId)) {
                return true; // Circular dependency detected
            }

            if (visited.has(nodeId)) {
                return false; // Already processed
            }

            visited.add(nodeId);
            recursionStack.add(nodeId);

            const dependencies = dependencyGraph.get(nodeId) || [];
            for (const depId of dependencies) {
                if (hasCycle(depId)) {
                    return true;
                }
            }

            recursionStack.delete(nodeId);
            return false;
        };

        for (const nodeId of dependencyGraph.keys()) {
            if (hasCycle(nodeId)) {
                throw new Error(`Circular dependency detected involving node: ${nodeId}`);
            }
        }
    }

    /**
     * Extract execution context từ inputData
     * inputData chỉ chứa outputs của trigger nodes đã xử lý ở BE App
     */
    private extractExecutionContext(inputData?: Record<string, any>): Record<string, any> {
        if (!inputData) {
            return {};
        }

        return {
            // Trigger node outputs - đây là output của webhook, schedule nodes đã chạy ở BE App
            triggerNodeOutputs: inputData.triggerNodeOutputs || inputData.outputs || [],

            // Hoặc có thể là direct outputs theo nodeId
            nodeOutputs: inputData.nodeOutputs || {},

            // Context data đơn giản
            contextData: inputData.contextData || inputData.data || {},

            // Raw inputData để backward compatibility
            rawInputData: inputData
        };
    }

    /**
     * Process node với execution context (đơn giản hóa)
     */
    private processNodeWithContext(nodeEntity: any, executionContext: Record<string, any>): Record<string, any> {
        if (!nodeEntity) {
            return { error: 'Node entity not found' };
        }

        const processedData: Record<string, any> = {
            nodeInfo: {
                id: nodeEntity.id,
                name: nodeEntity.name,
                type: nodeEntity.nodeDefinitionId,
                parameters: nodeEntity.parameters
            },
            availableInputs: {
                // Outputs từ trigger nodes (webhook, schedule, etc.)
                triggerNodeOutputs: Array.isArray(executionContext.triggerNodeOutputs) ?
                    executionContext.triggerNodeOutputs.length : 0,

                // Direct node outputs
                nodeOutputs: Object.keys(executionContext.nodeOutputs || {}).length,

                // Context data
                hasContextData: !!executionContext.contextData && Object.keys(executionContext.contextData).length > 0
            }
        };

        // List available trigger node outputs
        if (Array.isArray(executionContext.triggerNodeOutputs) && executionContext.triggerNodeOutputs.length > 0) {
            processedData.triggerOutputsSummary = executionContext.triggerNodeOutputs.map((output: any) => ({
                nodeId: output.nodeId || 'unknown',
                outputKeys: Object.keys(output.outputData || output || {}),
                dataSize: JSON.stringify(output.outputData || output || {}).length
            }));
        }

        // List available node outputs
        if (executionContext.nodeOutputs && Object.keys(executionContext.nodeOutputs).length > 0) {
            processedData.nodeOutputsSummary = Object.keys(executionContext.nodeOutputs).map(nodeId => ({
                nodeId,
                outputKeys: Object.keys(executionContext.nodeOutputs[nodeId] || {}),
                dataSize: JSON.stringify(executionContext.nodeOutputs[nodeId] || {}).length
            }));
        }

        // Context data summary
        if (executionContext.contextData && Object.keys(executionContext.contextData).length > 0) {
            processedData.contextDataSummary = {
                keys: Object.keys(executionContext.contextData),
                dataTypes: Object.entries(executionContext.contextData).reduce((acc, [key, value]) => {
                    acc[key] = typeof value;
                    return acc;
                }, {} as Record<string, string>)
            };
        }

        return processedData;
    }

    /**
     * Determine ready nodes based on dependencies and trigger outputs
     */
    private determineReadyNodes(
        nodes: any[],
        dependencyGraph: Map<string, string[]>,
        triggerNodeOutputs: Map<string, any>
    ): string[] {
        const readyNodes: string[] = [];

        for (const node of nodes) {
            const dependencies = dependencyGraph.get(node.id) || [];

            // If node has no dependencies, it's ready
            if (dependencies.length === 0) {
                // Skip if it's a trigger node that's already processed
                if (!triggerNodeOutputs.has(node.id)) {
                    readyNodes.push(node.id);
                }
                continue;
            }

            // Check if all dependencies are satisfied
            const allDependenciesSatisfied = dependencies.every(depId => {
                // Dependency is satisfied if:
                // 1. It's a trigger node with output, OR
                // 2. It's a completed processing node (for future iterations)
                return triggerNodeOutputs.has(depId);
            });

            if (allDependenciesSatisfied) {
                readyNodes.push(node.id);
            }
        }

        return readyNodes;
    }
}