"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WORKFLOW_STATES = exports.NODE_STATES = exports.EVENT_STATUS = exports.REDIS_PATTERNS = exports.EExecutionMode = exports.EExecutionType = void 0;
exports.isNodeEventPayload = isNodeEventPayload;
exports.isWorkflowEventPayload = isWorkflowEventPayload;
exports.isHealthCheckPayload = isHealthCheckPayload;
var EExecutionType;
(function (EExecutionType) {
    EExecutionType["TEST"] = "test";
    EExecutionType["EXECUTE"] = "execute";
})(EExecutionType || (exports.EExecutionType = EExecutionType = {}));
;
var EExecutionMode;
(function (EExecutionMode) {
    EExecutionMode["REALTIME"] = "realtime";
    EExecutionMode["BACKGROUND"] = "background";
})(EExecutionMode || (exports.EExecutionMode = EExecutionMode = {}));
;
;
function isNodeEventPayload(payload) {
    return payload && typeof payload === 'object' && 'nodeId' in payload;
}
function isWorkflowEventPayload(payload) {
    return payload && typeof payload === 'object' && 'workflowId' in payload && !('nodeId' in payload);
}
function isHealthCheckPayload(payload) {
    return payload && typeof payload === 'object' && ('checkType' in payload || 'requestId' in payload);
}
exports.REDIS_PATTERNS = {
    NODE_STARTED: 'node.started.*',
    NODE_PROCESSING: 'node.processing.*',
    NODE_COMPLETED: 'node.completed.*',
    NODE_FAILED: 'node.failed.*',
    WORKFLOW_COMPLETED: 'workflow.completed.*',
    WORKFLOW_FAILED: 'workflow.failed.*',
    HEALTH_CHECK: 'redis.health.check',
};
exports.EVENT_STATUS = {
    SUCCESS: 'success',
    FAILED: 'failed',
    PROCESSING: 'processing',
    PENDING: 'pending',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout',
};
exports.NODE_STATES = {
    IDLE: 'idle',
    STARTING: 'starting',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    RETRYING: 'retrying',
};
exports.WORKFLOW_STATES = {
    CREATED: 'created',
    QUEUED: 'queued',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    PAUSED: 'paused',
};
