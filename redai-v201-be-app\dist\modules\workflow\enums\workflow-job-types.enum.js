"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowJobType = void 0;
exports.isValidWorkflowJobType = isValidWorkflowJobType;
exports.getAllWorkflowJobTypes = getAllWorkflowJobTypes;
var WorkflowJobType;
(function (WorkflowJobType) {
    WorkflowJobType["USER_EXECUTE"] = "user_execute";
    WorkflowJobType["USER_EXECUTE_NODE"] = "user_execute_node";
    WorkflowJobType["ADMIN_EXECUTE"] = "admin_execute";
})(WorkflowJobType || (exports.WorkflowJobType = WorkflowJobType = {}));
function isValidWorkflowJobType(jobType) {
    return Object.values(WorkflowJobType).includes(jobType);
}
function getAllWorkflowJobTypes() {
    return Object.values(WorkflowJobType);
}
