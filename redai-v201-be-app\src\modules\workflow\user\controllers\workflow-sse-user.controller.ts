import { SubscriptionGuard } from '@/modules/subscription/guards/subscription.guard';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtUserGuard } from '@modules/auth/guards';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Req,
  Res,
  UseGuards
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { UserWorkflowSSEService } from '../services/workflow-sse-user.service';

/**
 * Controller xử lý Server-Sent Events cho workflow execution (User)
 * Cung cấp real-time updates về trạng thái thực hiện workflow và node cho user
 */
@ApiTags('User Workflow SSE')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@Controller('user/workflows/:workflowId')
export class WorkflowSSEUserController {
  private readonly logger = new Logger(WorkflowSSEUserController.name);

  constructor(
    private readonly userWorkflowSSEService: UserWorkflowSSEService,
  ) { }

  /**
   * Stream workflow events via Server-Sent Events
   */
  @Get('execution')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Stream workflow events via Server-Sent Events',
    description: 'Establishes an SSE connection to stream real-time workflow events and all node executions.',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'SSE stream established successfully',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  @ApiErrorResponse()
  async streamWorkflowEvents(
    @CurrentUser() user: JwtPayload,
    @Param('workflowId') workflowId: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const logger = new Logger('WorkflowSSEUserController');

    this.logger.log(`Creating SSE connection for user ${user.id} - workflow ${workflowId}`);

    try {
      // Lấy tất cả query params
      const queryParams = req.query as Record<string, string>;

      const nodeId = queryParams.nodeId;

      // Delegate to workflow SSE service
      this.userWorkflowSSEService.createSSEConnection(
        user.id,
        res,
        workflowId,
        nodeId, // Truyền nodeId từ query param
      );

    } catch (error) {
      // Send error response if headers haven't been sent yet
      if (!res.headersSent && !res.writableEnded) {
        try {
          res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to establish SSE stream',
            workflowId,
          });
        } catch (responseError) {
          logger.error(
            `Failed to send error response for workflow ${workflowId}:`,
            responseError,
          );
        }
      }
    }
  }
}
