"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicesModule = void 0;
const user_campaign_history_entity_1 = require("../../modules/marketing/user/entities/user-campaign-history.entity");
const api_key_encryption_helper_1 = require("../../modules/models/helpers/api-key-encryption.helper");
const pdf_edit_service_1 = require("./pdf/pdf-edit.service");
const tracking_link_util_1 = require("../utils/tracking-link.util");
const system_configuration_entity_1 = require("../../modules/system-configuration/entities/system-configuration.entity");
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const microservices_1 = require("@nestjs/microservices");
const typeorm_1 = require("@nestjs/typeorm");
const rag_api_interceptor_1 = require("../interceptors/rag-api.interceptor");
const google_1 = require("./google");
const marketing_ai_1 = require("./marketing-ai");
const recaptcha_service_1 = require("./recaptcha.service");
const redis_service_1 = require("./redis.service");
const run_status_service_1 = require("./run-status.service");
const sepay_hub_1 = require("./sepay-hub");
const anthropic_service_1 = require("./ai/anthropic.service");
const deepseek_service_1 = require("./ai/deepseek.service");
const google_ai_service_1 = require("./ai/google_ai.service");
const ai_provider_helper_1 = require("./ai/helpers/ai-provider.helper");
const openai_service_1 = require("./ai/openai.service");
const rag_file_processing_service_1 = require("./ai/rag-file-processing.service");
const rag_media_processing_service_1 = require("./ai/rag-media-processing.service");
const rag_product_processing_service_1 = require("./ai/rag-product-processing.service");
const xai_service_1 = require("./ai/xai.service");
const authenticator_module_1 = require("./authenticator/authenticator.module");
const automation_web_module_1 = require("./automation-web/automation-web.module");
const automation_web_service_1 = require("./automation-web/automation-web.service");
const cdn_service_1 = require("./cdn.service");
const email_tracking_service_1 = require("./email-tracking.service");
const encryption_1 = require("./encryption");
const encryption_service_1 = require("./encryption.service");
const facebook_module_1 = require("./facebook/facebook.module");
const paypal_module_1 = require("./paypal/paypal.module");
const s3_service_1 = require("./s3.service");
const ahamove_service_1 = require("./shipment/ahamove/ahamove.service");
const ghn_service_1 = require("./shipment/ghn/ghn.service");
const ghtk_service_1 = require("./shipment/ghtk/ghtk.service");
const sms_module_1 = require("./sms/sms.module");
const stripe_module_1 = require("./stripe/stripe.module");
const system_config_shared_service_1 = require("./system-config-shared.service");
const telegram_module_1 = require("./telegram/telegram.module");
const zalo_module_1 = require("./zalo/zalo.module");
let ServicesModule = class ServicesModule {
};
exports.ServicesModule = ServicesModule;
exports.ServicesModule = ServicesModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([system_configuration_entity_1.SystemConfiguration, user_campaign_history_entity_1.UserCampaignHistory]),
            axios_1.HttpModule,
            sms_module_1.SmsModule,
            sepay_hub_1.SepayHubModule,
            telegram_module_1.TelegramModule,
            zalo_module_1.ZaloModule,
            google_1.GoogleApiModule,
            authenticator_module_1.AuthenticatorModule,
            marketing_ai_1.MarketingAiModule,
            facebook_module_1.FacebookModule,
            paypal_module_1.PayPalModule,
            stripe_module_1.StripeModule,
            automation_web_module_1.AutomationWebModule,
            config_1.ConfigModule,
            microservices_1.ClientsModule.registerAsync([
                {
                    name: 'REDIS_CLIENT',
                    imports: [config_1.ConfigModule],
                    useFactory: async (configService) => {
                        const redisUrl = configService.get('REDIS_URL', 'redis://localhost:6379');
                        const url = new URL(redisUrl);
                        return {
                            transport: microservices_1.Transport.REDIS,
                            options: {
                                host: url.hostname,
                                port: parseInt(url.port) || 6379,
                                retryAttempts: 5,
                                retryDelay: 3000,
                                wildcards: true,
                            },
                        };
                    },
                    inject: [config_1.ConfigService],
                },
            ]),
        ],
        providers: [
            system_config_shared_service_1.SystemConfigSharedService,
            s3_service_1.S3Service,
            openai_service_1.OpenAiService,
            anthropic_service_1.AnthropicService,
            google_ai_service_1.GoogleAIService,
            deepseek_service_1.DeepSeekService,
            xai_service_1.XAIService,
            rag_file_processing_service_1.RagFileProcessingService,
            rag_media_processing_service_1.RagMediaProcessingService,
            rag_product_processing_service_1.RagProductProcessingService,
            ai_provider_helper_1.AiProviderHelper,
            cdn_service_1.CdnService,
            redis_service_1.RedisService,
            run_status_service_1.RunStatusService,
            recaptcha_service_1.RecaptchaService,
            pdf_edit_service_1.PdfEditService,
            {
                provide: 'SimpleEncryptionService',
                useClass: encryption_service_1.EncryptionService,
            },
            encryption_1.EncryptionService,
            encryption_1.KeyPairEncryptionService,
            api_key_encryption_helper_1.ApiKeyEncryptionHelper,
            rag_api_interceptor_1.RagApiInterceptor,
            email_tracking_service_1.EmailTrackingService,
            tracking_link_util_1.TrackingLinkUtil,
            ghn_service_1.GHNService,
            ghtk_service_1.GHTKService,
            ahamove_service_1.AhamoveService,
            encryption_1.EncryptionWebsiteService,
            automation_web_service_1.AutomationWebService,
        ],
        exports: [
            system_config_shared_service_1.SystemConfigSharedService,
            s3_service_1.S3Service,
            openai_service_1.OpenAiService,
            anthropic_service_1.AnthropicService,
            google_ai_service_1.GoogleAIService,
            deepseek_service_1.DeepSeekService,
            xai_service_1.XAIService,
            rag_file_processing_service_1.RagFileProcessingService,
            rag_media_processing_service_1.RagMediaProcessingService,
            rag_product_processing_service_1.RagProductProcessingService,
            ai_provider_helper_1.AiProviderHelper,
            cdn_service_1.CdnService,
            redis_service_1.RedisService,
            run_status_service_1.RunStatusService,
            recaptcha_service_1.RecaptchaService,
            pdf_edit_service_1.PdfEditService,
            'SimpleEncryptionService',
            encryption_1.EncryptionService,
            encryption_1.KeyPairEncryptionService,
            api_key_encryption_helper_1.ApiKeyEncryptionHelper,
            sepay_hub_1.SepayHubModule,
            telegram_module_1.TelegramModule,
            zalo_module_1.ZaloModule,
            google_1.GoogleApiModule,
            authenticator_module_1.AuthenticatorModule,
            marketing_ai_1.MarketingAiModule,
            facebook_module_1.FacebookModule,
            paypal_module_1.PayPalModule,
            stripe_module_1.StripeModule,
            rag_api_interceptor_1.RagApiInterceptor,
            email_tracking_service_1.EmailTrackingService,
            tracking_link_util_1.TrackingLinkUtil,
            ghn_service_1.GHNService,
            ghtk_service_1.GHTKService,
            ahamove_service_1.AhamoveService,
            encryption_1.EncryptionWebsiteService,
            automation_web_module_1.AutomationWebModule,
            automation_web_service_1.AutomationWebService,
            microservices_1.ClientsModule,
        ],
    })
], ServicesModule);
