"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const schedule_1 = require("@nestjs/schedule");
const data_module_1 = require("./modules/data/data.module");
const database_module_1 = require("./shared/database/database.module");
const services_module_1 = require("./shared/services/services.module");
const agent_module_1 = require("./modules/agent/agent.module");
const auth_module_1 = require("./modules/auth/auth.module");
const user_module_1 = require("./modules/user/user.module");
const _config_1 = require("./config");
const subscription_module_1 = require("./modules/subscription/subscription.module");
const affiliate_module_1 = require("./modules/affiliate/affiliate.module");
const marketing_user_module_1 = require("./modules/marketing/user/marketing-user.module");
const marketing_module_1 = require("./modules/marketing/marketing.module");
const marketplace_module_1 = require("./modules/marketplace/marketplace.module");
const integration_module_1 = require("./modules/integration/integration.module");
const helper_module_1 = require("./common/helpers/helper.module");
const employee_module_1 = require("./modules/employee/employee.module");
const knowledge_files_module_1 = require("./modules/data/knowledge-files/knowledge-files.module");
const common_2 = require("./common");
const common_module_1 = require("./modules/common/common.module");
const system_configuration_1 = require("./modules/system-configuration");
const blog_module_1 = require("./modules/blog/blog.module");
const r_point_module_1 = require("./modules/r-point/r-point.module");
const google_module_1 = require("./modules/google/google.module");
const business_module_1 = require("./modules/business/business.module");
const public_business_module_1 = require("./modules/business/public/public-business.module");
const tools_module_1 = require("./modules/tools/tools.module");
const tools_build_in_module_1 = require("./modules/tools-build-in/tools-build-in.module");
const queue_module_1 = require("./shared/queue/queue.module");
const chat_module_1 = require("./modules/chat/chat.module");
const rule_contract_module_1 = require("./modules/rule-contract/rule-contract.module");
const models_module_1 = require("./modules/models/models.module");
const page_keywords_module_1 = require("./modules/page-keywords/page-keywords.module");
const help_center_module_1 = require("./modules/help-center/help-center.module");
const device_session_module_1 = require("./modules/device-session/device-session.module");
const location_module_1 = require("./modules/location/location.module");
const debug_module_1 = require("./debug/debug.module");
const internal_module_1 = require("./modules/internal/internal.module");
const dashboard_module_1 = require("./modules/dashboard/dashboard.module");
const analytics_module_1 = require("./modules/analytics/analytics.module");
const zalo_sdk_1 = require("./shared/services/zalo-sdk");
const webhook_module_1 = require("./shared/webhook/webhook.module");
const webhook_config_admin_module_1 = require("./modules/webhook-config/admin/webhook-config-admin.module");
const signature_module_1 = require("./modules/signature/signature.module");
const i18n_common_module_1 = require("./common/i18n-common.module");
const calendar_module_1 = require("./modules/calendar/calendar.module");
const workflow_module_1 = require("./modules/workflow/workflow.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(common_2.RequestLoggerMiddleware).forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            _config_1.ConfigModule,
            database_module_1.DatabaseModule,
            services_module_1.ServicesModule,
            helper_module_1.HelperModule,
            common_2.CommonModule,
            common_module_1.CommonModule,
            i18n_common_module_1.I18nCommonModule,
            queue_module_1.QueueModule,
            zalo_sdk_1.ZaloSdkModule,
            webhook_module_1.WebhookModule,
            event_emitter_1.EventEmitterModule.forRoot(),
            schedule_1.ScheduleModule.forRoot(),
            data_module_1.DataModule,
            agent_module_1.AgentModule,
            user_module_1.UserModule,
            auth_module_1.AuthModule,
            affiliate_module_1.AffiliateModule,
            marketing_user_module_1.MarketingUserModule,
            marketing_module_1.MarketingModule,
            marketplace_module_1.MarketplaceModule,
            subscription_module_1.SubscriptionModule,
            integration_module_1.IntegrationModule,
            employee_module_1.EmployeeModule,
            knowledge_files_module_1.KnowledgeFilesModule,
            employee_module_1.EmployeeModule,
            system_configuration_1.SystemConfigurationModule,
            blog_module_1.BlogModule,
            r_point_module_1.RPointModule,
            google_module_1.GoogleModule,
            business_module_1.BusinessModule,
            public_business_module_1.PublicBusinessModule,
            tools_module_1.ToolsModule,
            tools_build_in_module_1.ToolsBuildInModule,
            chat_module_1.ChatModule,
            rule_contract_module_1.RuleContractModule,
            models_module_1.ModelsModule,
            calendar_module_1.CalendarModule,
            workflow_module_1.WorkflowModule,
            page_keywords_module_1.PageKeywordsModule,
            help_center_module_1.HelpCenterModule,
            device_session_module_1.DeviceSessionModule,
            location_module_1.LocationModule,
            internal_module_1.InternalModule,
            dashboard_module_1.DashboardModule,
            analytics_module_1.AnalyticsModule,
            webhook_config_admin_module_1.WebhookConfigAdminModule,
            signature_module_1.SignatureModule,
            ...(process.env.NODE_ENV === 'development' ? [debug_module_1.DebugModule] : []),
        ],
    })
], AppModule);
