import { UserCampaignHistory } from '@/modules/marketing/user/entities/user-campaign-history.entity';
import { ApiKeyEncryptionHelper } from '@/modules/models/helpers/api-key-encryption.helper';
import { PdfEditService } from '@/shared/services/pdf/pdf-edit.service';
import { TrackingLinkUtil } from '@/shared/utils/tracking-link.util';
import { SystemConfiguration } from '@modules/system-configuration/entities/system-configuration.entity';
import { HttpModule } from '@nestjs/axios';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RagApiInterceptor } from '@shared/interceptors/rag-api.interceptor';
import { GoogleApiModule } from '@shared/services/google';
import { MarketingAiModule } from '@shared/services/marketing-ai';
import { RecaptchaService } from '@shared/services/recaptcha.service';
import { RedisService } from '@shared/services/redis.service';
import { RunStatusService } from '@shared/services/run-status.service';
import { SepayHubModule } from '@shared/services/sepay-hub';
import { AnthropicService } from './ai/anthropic.service';
import { DeepSeekService } from './ai/deepseek.service';
import { GoogleAIService } from './ai/google_ai.service';
import { AiProviderHelper } from './ai/helpers/ai-provider.helper';
import { OpenAiService } from './ai/openai.service';
import { RagFileProcessingService } from './ai/rag-file-processing.service';
import { RagMediaProcessingService } from './ai/rag-media-processing.service';
import { RagProductProcessingService } from './ai/rag-product-processing.service';
import { XAIService } from './ai/xai.service';
import { AuthenticatorModule } from './authenticator/authenticator.module';
import { AutomationWebModule } from './automation-web/automation-web.module';
import { AutomationWebService } from './automation-web/automation-web.service';
import { CdnService } from './cdn.service';
import { EmailTrackingService } from './email-tracking.service';
import { EncryptionService, EncryptionWebsiteService, KeyPairEncryptionService } from './encryption';
import { EncryptionService as SimpleEncryptionService } from './encryption.service';
import { FacebookModule } from './facebook/facebook.module';
import { PayPalModule } from './paypal/paypal.module';
import { S3Service } from './s3.service';
import { AhamoveService } from './shipment/ahamove/ahamove.service';
import { GHNService } from './shipment/ghn/ghn.service';
import { GHTKService } from './shipment/ghtk/ghtk.service';
import { SmsModule } from './sms/sms.module';
import { StripeModule } from './stripe/stripe.module';
import { SystemConfigSharedService } from './system-config-shared.service';
import { TelegramModule } from './telegram/telegram.module';
import { ZaloModule } from './zalo/zalo.module';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([SystemConfiguration, UserCampaignHistory]),
    HttpModule,
    SmsModule,
    SepayHubModule,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    FacebookModule,
    PayPalModule,
    StripeModule,
    AutomationWebModule,
    ConfigModule,
    ClientsModule.registerAsync([
      {
        name: 'REDIS_CLIENT',
        imports: [ConfigModule],
        useFactory: async (configService: ConfigService) => {
          const redisUrl = configService.get<string>('REDIS_URL', 'redis://localhost:6379');
          const url = new URL(redisUrl);

          return {
            transport: Transport.REDIS,
            options: {
              host: url.hostname,
              port: parseInt(url.port) || 6379,
              retryAttempts: 5,
              retryDelay: 3000,
              wildcards: true,
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  providers: [
    SystemConfigSharedService,
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    XAIService,
    RagFileProcessingService,
    RagMediaProcessingService,
    RagProductProcessingService,
    AiProviderHelper,
    CdnService,
    RedisService,
    RunStatusService,
    RecaptchaService,
    PdfEditService,
    {
      provide: 'SimpleEncryptionService',
      useClass: SimpleEncryptionService,
    },
    EncryptionService,
    KeyPairEncryptionService,
    ApiKeyEncryptionHelper,
    RagApiInterceptor,
    EmailTrackingService,
    TrackingLinkUtil,
    GHNService,
    GHTKService,
    AhamoveService,
    EncryptionWebsiteService,
    AutomationWebService,
  ],
  exports: [
    SystemConfigSharedService,
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    XAIService,
    RagFileProcessingService,
    RagMediaProcessingService,
    RagProductProcessingService,
    AiProviderHelper,
    CdnService,
    RedisService,
    RunStatusService,
    RecaptchaService,
    PdfEditService,
    'SimpleEncryptionService',
    EncryptionService,
    KeyPairEncryptionService,
    ApiKeyEncryptionHelper,
    SepayHubModule,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    FacebookModule,
    PayPalModule,
    StripeModule,
    RagApiInterceptor,
    EmailTrackingService,
    TrackingLinkUtil,
    GHNService,
    GHTKService,
    AhamoveService,
    EncryptionWebsiteService,
    AutomationWebModule,
    AutomationWebService,
    ClientsModule,
  ],
})
export class ServicesModule { }
