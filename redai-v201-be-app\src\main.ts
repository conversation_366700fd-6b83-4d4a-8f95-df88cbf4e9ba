import { corsConfig, websiteChatCorsConfig } from '@common/filters/cors.config';
import { createSwaggerConfig, swaggerCustomOptions } from '@common/swagger';
import { initializeTypeOrmTransactional } from '@config/typeorm-transactional.config';
import { VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import * as connectRedis from 'connect-redis';
import * as dotenv from 'dotenv';
import * as session from 'express-session';
import helmet from 'helmet';
import { join } from 'path';
import { createClient } from 'redis';
import 'tsconfig-paths/register';
import { AppModule } from './app.module';
// Khởi tạo typeorm-transactional
initializeTypeOrmTransactional();

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Cấu hình Session với Redis store
  const configService = app.get(ConfigService);

  const url = new URL(configService.get<string>('REDIS_URL') || 'redis://localhost:6379');

  // Connect Redis microservice
  app.connectMicroservice<MicroserviceOptions>(
    {
      transport: Transport.REDIS,
      options: {
        host: url.hostname, // Sử dụng hostname thay vì host
        port: parseInt(url.port) || 6379, // Convert string to number với fallback
        retryAttempts: 5,
        retryDelay: 3000,
        wildcards: true,
      },
    }
  );

  // Start all microservices
  await app.startAllMicroservices();

  // THIS IS THE KEY LINE
  // It tells Express to trust the X-Forwarded-For header that your
  // proxy (like Nginx, a load balancer, or Docker's ingress) sets.
  app.getHttpAdapter().getInstance().set('trust proxy', true);
  app.use(helmet());

  // Tạo Redis client cho session store
  const redisClient = createClient({
    url: configService.get<string>('REDIS_URL') || 'redis://localhost:6379',
  });

  redisClient.connect().catch(console.error);

  // Cấu hình session store với connect-redis v6
  const RedisStore = connectRedis(session);

  app.use(
    session({
      store: new RedisStore({ client: redisClient }),
      secret: configService.get<string>('SESSION_SECRET') || 'your-session-secret-key',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: false, // Set to true if using HTTPS
        httpOnly: true,
        maxAge: 1000 * 60 * 60 * 24 * 7, // 7 days
      },
    }),
  );

  // Phục vụ tệp tĩnh từ thư mục public
  app.getHttpAdapter().getInstance().useStaticAssets(join(__dirname, '..', 'src', 'public'), {
    prefix: '/public/',
  });

  // Cấu hình giới hạn kích thước request body với xử lý JSON an toàn
  app.use(bodyParser.json({
    limit: '50mb',
    verify: (req: any, res: any, buf: Buffer) => {
      try {
        const jsonString = buf.toString('utf8');
        // Kiểm tra và log chi tiết nếu có ký tự điều khiển
        const controlChars = jsonString.match(/[\x00-\x1F\x7F]/g);
        if (controlChars) {
          console.warn('Found control characters in JSON:', {
            chars: controlChars.map(c => `\\x${c.charCodeAt(0).toString(16).padStart(2, '0')}`),
            positions: controlChars.map(c => jsonString.indexOf(c)),
            url: req.url
          });
        }

        // Thử parse JSON gốc trước
        JSON.parse(jsonString);
      } catch (error) {
        console.error('JSON parsing error details:', {
          error: error.message,
          url: req.url,
          method: req.method,
          contentType: req.headers['content-type'],
          bodyLength: buf.length,
          bodyPreview: buf.toString('utf8').substring(0, 500)
        });

        // Thử làm sạch và parse lại
        try {
          const cleanedJson = buf.toString('utf8').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
          JSON.parse(cleanedJson);
          console.log('Successfully cleaned and parsed JSON');
        } catch (cleanError) {
          console.error('Even cleaned JSON failed to parse:', cleanError.message);
        }

        throw error;
      }
    }
  }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  // app.setGlobalPrefix('api');

  // Thêm global prefix với version
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  })

  // I18nValidationPipe đã được cấu hình trong I18nCommonModule

  // Cấu hình CORS với route-specific handling
  // Website chat routes cho phép tất cả origins để embed được
  app.use('/v1/website/chat', (req, res, next) => {
    const corsOptions = websiteChatCorsConfig;
    const cors = require('cors');
    cors(corsOptions)(req, res, next);
  });

  app.use('/v1/website/platform', (req, res, next) => {
    const corsOptions = websiteChatCorsConfig;
    const cors = require('cors');
    cors(corsOptions)(req, res, next);
  });

  // Các routes khác sử dụng CORS hạn chế
  app.enableCors(corsConfig);

  // Cấu hình Swagger với thông tin từ biến môi trường
  // configService đã được khởi tạo ở trên cho session
  const swaggerConfig = createSwaggerConfig(configService);
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api/docs', app, document, swaggerCustomOptions);

  // Khởi động server
  const port = process.env.PORT ?? 3000;

  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
  console.log(`SSE Test page is available at: http://localhost:${port}/public/sse-test.html`);
}
bootstrap().catch((err) => {
  console.error(err);
  process.exit(1);
});