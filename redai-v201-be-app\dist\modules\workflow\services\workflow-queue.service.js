"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WorkflowQueueService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowQueueService = void 0;
const bullmq_1 = require("@nestjs/bullmq");
const common_1 = require("@nestjs/common");
const bullmq_2 = require("bullmq");
const queue_constants_1 = require("../../../shared/queue/queue.constants");
const workflow_job_types_enum_1 = require("../enums/workflow-job-types.enum");
const interfaces_1 = require("../interfaces");
let WorkflowQueueService = WorkflowQueueService_1 = class WorkflowQueueService {
    workflowQueue;
    logger = new common_1.Logger(WorkflowQueueService_1.name);
    constructor(workflowQueue) {
        this.workflowQueue = workflowQueue;
    }
    async executeUser(payload) {
        const job = await this.workflowQueue.add(workflow_job_types_enum_1.WorkflowJobType.USER_EXECUTE, payload, {
            jobId: payload.executionId,
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 3000,
            },
            removeOnComplete: 100,
            removeOnFail: 50,
            priority: payload.type === interfaces_1.EExecutionType.TEST ? 10 : 5,
        });
        this.logger.log(`Workflow execution job added with ID: ${job.id}`);
        return { jobId: job.id, status: 'queued' };
    }
    async getJobStatus(jobId) {
        const job = await this.workflowQueue.getJob(jobId);
        if (!job) {
            return null;
        }
        return {
            id: job.id,
            status: await job.getState(),
            progress: job.progress,
            data: job.data,
            returnvalue: job.returnvalue,
            failedReason: job.failedReason,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
        };
    }
    async cancelJob(jobId) {
        const job = await this.workflowQueue.getJob(jobId);
        if (!job) {
            return false;
        }
        await job.remove();
        this.logger.log(`Job ${jobId} cancelled`);
        return true;
    }
};
exports.WorkflowQueueService = WorkflowQueueService;
exports.WorkflowQueueService = WorkflowQueueService = WorkflowQueueService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bullmq_1.InjectQueue)(queue_constants_1.QueueName.WORKFLOW_EXECUTION)),
    __metadata("design:paramtypes", [bullmq_2.Queue])
], WorkflowQueueService);
