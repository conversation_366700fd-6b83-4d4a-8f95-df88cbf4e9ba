"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cors_config_1 = require("./common/filters/cors.config");
const swagger_1 = require("./common/swagger");
const typeorm_transactional_config_1 = require("./config/typeorm-transactional.config");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const microservices_1 = require("@nestjs/microservices");
const swagger_2 = require("@nestjs/swagger");
const bodyParser = require("body-parser");
const connectRedis = require("connect-redis");
const dotenv = require("dotenv");
const session = require("express-session");
const helmet_1 = require("helmet");
const path_1 = require("path");
const redis_1 = require("redis");
require("tsconfig-paths/register");
const app_module_1 = require("./app.module");
(0, typeorm_transactional_config_1.initializeTypeOrmTransactional)();
dotenv.config();
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const url = new URL(configService.get('REDIS_URL') || 'redis://localhost:6379');
    app.connectMicroservice({
        transport: microservices_1.Transport.REDIS,
        options: {
            host: url.hostname,
            port: parseInt(url.port) || 6379,
            retryAttempts: 5,
            retryDelay: 3000,
            wildcards: true,
        },
    });
    await app.startAllMicroservices();
    app.getHttpAdapter().getInstance().set('trust proxy', true);
    app.use((0, helmet_1.default)());
    const redisClient = (0, redis_1.createClient)({
        url: configService.get('REDIS_URL') || 'redis://localhost:6379',
    });
    redisClient.connect().catch(console.error);
    const RedisStore = connectRedis(session);
    app.use(session({
        store: new RedisStore({ client: redisClient }),
        secret: configService.get('SESSION_SECRET') || 'your-session-secret-key',
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: false,
            httpOnly: true,
            maxAge: 1000 * 60 * 60 * 24 * 7,
        },
    }));
    app.getHttpAdapter().getInstance().useStaticAssets((0, path_1.join)(__dirname, '..', 'src', 'public'), {
        prefix: '/public/',
    });
    app.use(bodyParser.json({
        limit: '50mb',
        verify: (req, res, buf) => {
            try {
                const jsonString = buf.toString('utf8');
                const controlChars = jsonString.match(/[\x00-\x1F\x7F]/g);
                if (controlChars) {
                    console.warn('Found control characters in JSON:', {
                        chars: controlChars.map(c => `\\x${c.charCodeAt(0).toString(16).padStart(2, '0')}`),
                        positions: controlChars.map(c => jsonString.indexOf(c)),
                        url: req.url
                    });
                }
                JSON.parse(jsonString);
            }
            catch (error) {
                console.error('JSON parsing error details:', {
                    error: error.message,
                    url: req.url,
                    method: req.method,
                    contentType: req.headers['content-type'],
                    bodyLength: buf.length,
                    bodyPreview: buf.toString('utf8').substring(0, 500)
                });
                try {
                    const cleanedJson = buf.toString('utf8').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
                    JSON.parse(cleanedJson);
                    console.log('Successfully cleaned and parsed JSON');
                }
                catch (cleanError) {
                    console.error('Even cleaned JSON failed to parse:', cleanError.message);
                }
                throw error;
            }
        }
    }));
    app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
    app.enableVersioning({
        type: common_1.VersioningType.URI,
        defaultVersion: '1',
    });
    app.use('/v1/website/chat', (req, res, next) => {
        const corsOptions = cors_config_1.websiteChatCorsConfig;
        const cors = require('cors');
        cors(corsOptions)(req, res, next);
    });
    app.use('/v1/website/platform', (req, res, next) => {
        const corsOptions = cors_config_1.websiteChatCorsConfig;
        const cors = require('cors');
        cors(corsOptions)(req, res, next);
    });
    app.enableCors(cors_config_1.corsConfig);
    const swaggerConfig = (0, swagger_1.createSwaggerConfig)(configService);
    const document = swagger_2.SwaggerModule.createDocument(app, swaggerConfig);
    swagger_2.SwaggerModule.setup('api/docs', app, document, swagger_1.swaggerCustomOptions);
    const port = process.env.PORT ?? 3000;
    await app.listen(port);
    console.log(`Application is running on: http://localhost:${port}`);
    console.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
    console.log(`SSE Test page is available at: http://localhost:${port}/public/sse-test.html`);
}
bootstrap().catch((err) => {
    console.error(err);
    process.exit(1);
});
