"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WebhookTriggerController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookTriggerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const webhook_trigger_service_1 = require("../services/webhook-trigger.service");
let WebhookTriggerController = WebhookTriggerController_1 = class WebhookTriggerController {
    webhookTriggerService;
    logger = new common_1.Logger(WebhookTriggerController_1.name);
    constructor(webhookTriggerService) {
        this.webhookTriggerService = webhookTriggerService;
    }
    async handleWebhook(webhookId, request, response, body, headers) {
        const startTime = Date.now();
        try {
            await this.webhookTriggerService.triggerWebhook(webhookId, request, body, headers);
            const responseTime = Date.now() - startTime;
            response.status(200).json({
                status: 'received',
                message: 'Webhook processed successfully',
                webhookId,
                responseTime
            });
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            if (error instanceof common_1.HttpException) {
                response.status(error.getStatus()).json(error.getResponse());
            }
            else {
                this.logger.error('Failed to process webhook:', error);
                response.status(500).json({
                    error: 'Internal server error',
                    webhookId,
                    message: 'An unexpected error occurred while processing the webhook',
                    timestamp: new Date().toISOString(),
                    responseTime: `${responseTime}ms`,
                });
            }
        }
    }
};
exports.WebhookTriggerController = WebhookTriggerController;
__decorate([
    (0, common_1.All)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Handle webhook trigger',
        description: 'Receives webhook data and triggers workflow execution',
    }),
    (0, swagger_1.ApiParam)({
        name: 'webhookId',
        description: 'Unique webhook identifier',
        type: 'string',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Webhook payload data',
        schema: {
            type: 'object',
            example: {
                userId: 12345,
                action: 'created',
                timestamp: '2024-01-01T00:00:00Z',
                data: {
                    id: 'item-123',
                    name: 'Sample Item'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Webhook processed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                webhookId: { type: 'string', example: 'webhook-123' },
                executionId: { type: 'string', example: 'exec-456' },
                timestamp: { type: 'string', example: '2024-01-01T00:00:00Z' }
            }
        }
    }),
    __param(0, (0, common_1.Param)('webhookId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __param(3, (0, common_1.Body)()),
    __param(4, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebhookTriggerController.prototype, "handleWebhook", null);
exports.WebhookTriggerController = WebhookTriggerController = WebhookTriggerController_1 = __decorate([
    (0, swagger_1.ApiTags)('Webhook Triggers'),
    (0, common_1.Controller)('/webhooks/:webhookId'),
    __metadata("design:paramtypes", [webhook_trigger_service_1.WebhookTriggerService])
], WebhookTriggerController);
